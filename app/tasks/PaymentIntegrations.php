<?php

use <PERSON>al<PERSON>\Mvc\Controller;
use ControllerBase as base;

class PaymentIntegrations extends Controller {

    /**
     * @ControllerBase
     * @var type 
     */
    protected $base;

    function onConstruct() {
        $this->base = new base();
    }

    /**
     * GetC2BAccessTokey
     * @param type $paybillNumber
     * @param type $prConsumerKey
     * @param type $prSecretKey
     * @return array
     */
    public function GetC2BAccessTokey($paybillNumber, $prConsumerKey, $prSecretKey): array {
        try {
            $key = 'CheckOutKey$JavaAPP';
            $queryToken = RedisUtils::redisRawSelectData($key);
            if ($queryToken) {
                return [
                    'code' => 200,
                    'message' => 'Queried Token for paybill ' . $paybillNumber . ' Successfully',
                    'access_token' => $queryToken->access_token];
            }

            RedisUtils::redisRawDelete($key);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->base
                    ->settings['mnoApps']['Urls']['AuthUrl']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            $headers = [
                'Authorization: Basic ' . base64_encode("$prConsumerKey:$prSecretKey")];
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
            //timeouts
            curl_setopt($ch, CURLOPT_TIMEOUT, $this->base->settings['timeoutDuration']);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $this->base->settings['connectTimeout']);
            //accept SSL settings
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, $this->base->settings['appName'] . "/3.0");
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $result = json_decode(curl_exec($ch));
            $returnCode = (int) curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                    . " [$paybillNumber]"
                    . " | URL:" . $this->base->settings['mnoApps']['Urls']['AuthUrl']
                    . " | OAuth2 REQUEST::" . json_encode(["statusCode" => $returnCode
                        , "response" => $result, "error" => $curlError]));

            if ($returnCode != 200) {
                if ($returnCode == 0) {
                    return ['code' => 502,
                        'message' => "Connection timeout on OAUTH2 Request. $curlError"];
                }

                $errorMessage = isset($result->errorMessage) ? $result->errorMessage : "";
                if ($returnCode == 500) {
                    return ['code' => $returnCode,
                        'message' => "Internal server error. $errorMessage"];
                }

                if (!$result || $result == null) {
                    return ['code' => $returnCode,
                        'message' => "Mpesa Authetication returned a null response."];
                }

                return ['code' => $returnCode,
                    'message' => "Authentication Failed. $errorMessage"];
            }

            RedisUtils::redisRawInsertData($key,
                    ['access_token' => $result->access_token],
                    50 * 60);

            return [
                'code' => 200,
                'message' => 'Queried Token Successfully',
                'access_token' => $result->access_token];
        } catch (Exception $ex) {
            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . " [$paybillNumber]"
                    . " | Trace::" . $ex->getTraceAsString()
                    . " | Exception::" . $ex->getMessage());
            return [
                'code' => 500,
                'message' => 'An Exception occured. ' . $ex->getMessage()];
        }
    }

}
