<?php

/**
 * Description of Queue
 *
 * <AUTHOR>
 */
use <PERSON>al<PERSON>\Mvc\Controller;
use ControllerBase as base;
use PhpAmqpLib\Connection\AMQPConnection;
use PhpAmqpLib\Message\AMQPMessage;

class Queue extends Controller {

    protected $base;

    function onConstruct() {
        $this->base = new base();
    }

    /**
     * ConnectAndPublishToQueue
     * @param type $payload
     * @param type $queueName
     * @param type $exchangeKey
     * @param type $routeKey
     * @param type $server
     * @param type $port
     * @param type $user
     * @param type $pass
     * @param type $vhost
     * @return \stdClass
     */
    public function ConnectAndPublishToQueue($payload, $queueName, $exchangeKey, $routeKey
            , $server = null, $port = null, $user = null, $pass = null, $vhost = "/", $prefix = null) {
        $response = new \stdClass();

        $start = $this->base->getMicrotime();

        if (!$queueName || !$exchangeKey || !$routeKey) {
            $response->code = 422;
            $response->statusDescription = "Mandatory Fields are missing!!";
            $response->data = [];

            return $response;
        }

        if (!$prefix || $prefix == null) {
            $prefix = "MOSSBETS";
        }

        $prefix = strtoupper($prefix);

        $queueName = $prefix . '_' . strtoupper($queueName) . '_QUEUE'; //queue name
        $exchangeKey = $prefix . '_' . strtoupper($exchangeKey) . '_EXCHANGE'; //queue exchange,
        $routeKey = $prefix . '_' . strtoupper($routeKey) . '_ROUTE'; //queue routing

        $rabbitMQ = $this->queue;
        if ($server == null) {
            $server = $rabbitMQ['rabbitServer'];
        }

        if ($port == null) {
            $port = $rabbitMQ['rabbitPortNo'];
        }

        if ($user == null) {
            $user = $rabbitMQ['rabbitUser'];
        }

        if ($pass == null) {
            $pass = $rabbitMQ['rabbitPass'];
        }

        $conn = false;
        try {
            $conn = new AMQPConnection($server, $port, $user, $pass, $vhost);
        } catch (Exception $ex) {
            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . " | Took " . $this->base->CalculateTAT($start) . " Sec"
                    . " | [$queueName]"
                    . " | QUEUE SERVICE:: Exception on AMQPConnection::" . $ex->getMessage());
        }

        if (is_array($payload)) {
            $payload = json_encode($payload);
        }

        if (!$conn) {
            $response->code = 401;
            $response->statusDescription = "Rabbit Connection Failed";

            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->base->CalculateTAT($start) . " Sec"
                    . "| [$queueName]"
                    . "| QUEUE SERVICE:: Message:{$payload}"
                    . "| Connection Failed!");

            return $response;
        }


        try {
            $channel = $conn->channel();
            $channel->queue_declare($queueName, false, true, false, false);
            $channel->exchange_declare($exchangeKey, 'direct', false, true, false);
            $channel->queue_bind($queueName, $exchangeKey, $routeKey);
            $channel->basic_publish(new AMQPMessage($payload, array('delivery_mode' => 2)), $exchangeKey, $routeKey);
            $channel->close();
            $conn->close();

            $response->code = 200;
            $response->statusDescription = "Successfully published on Queue:$queueName";
            $response->data = [
                "queue" => $queueName,
                "status" => "success",
                "time" => date("Y-m-d H:i:s")];

            $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                    . " | Took " . $this->base->CalculateTAT($start) . " Sec"
                    . " | [$queueName]"
                    . " | QUEUE SERVICE:: Message:{$payload}"
                    . " | Successfully Published!");

            return $response;
        } catch (Exception $ex) {
            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . " | Took " . $this->base->CalculateTAT($start) . " Sec"
                    . " | [$queueName]"
                    . " | QUEUE SERVICE:: Exception on Publishing Message::" . $ex->getMessage());
            $response->code = 500;
            $response->statusDescription = $ex->getMessage();
        }

        return $response;
    }

}
