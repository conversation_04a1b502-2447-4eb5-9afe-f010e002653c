<?php

use <PERSON>al<PERSON>\Mvc\Controller;
use ControllerBase as base;

class Utilities extends Controller {

    protected $base;

    function onConstruct() {
        $this->base = new base();
    }

    /**
     * SaveCallBackResponse
     * @param type $param
     * @return boolean
     */
    public function SaveCallBackResponse($param) {
        try {
            $res = $this->base->rawSelectOneRecord('dbWrite',
                    "SELECT id,mpesa_code FROM callback_responses "
                    . "WHERE mpesa_code=:mpesa_code AND type=:type",
                    [':mpesa_code' => $param['mpesa_code'],
                        ':type' => $param['type']]);
            if ($res) {
                return $res['id'];
            }

            return $this->base->rawInsertBulk('dbWrite', 'callback_responses', $param);
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * GetMpesaMSISDNHash
     * @param type $msisdn
     * @return boolean
     */
    public function GetMpesaMSISDNHash($msisdn) {
        try {
            return hash('sha256', $msisdn);
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * GetMSISDN
     * @param type $hashedMsisdn
     * @return boolean
     */
    public function GetMSISDN($hashedMsisdn) {
        try {
            $profile = $this->base->rawSelectOneRecord('dbWrite',
                    "select msisdn from profile where msisdn_hash=:msisdn_hash",
                    [':msisdn_hash' => $hashedMsisdn]);
            if (!$profile) {
                return false;
            }

            return $profile['msisdn'];
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * GetProfile
     * @param type $params
     * @return boolean
     */
    public function GetProfile($params) {
        try {
            $profile = $this->base->rawSelectOneRecord('dbWrite',
                    "select id,first_name,middle_name,surname,msisdn_hash,status,account_number "
                    . "from profile where msisdn=:msisdn", [':msisdn' => $params['msisdn']]);
            if (!$profile) {
                return ['id' => $this->base->rawInsertBulk('dbWrite',
                            'profile', ['msisdn' => $params['msisdn'],
                        'account_number' => $this->base->ReferenceNumber(),
                        'first_name' => $params['first_name'] ?? null,
                        'middle_name' => $params['middle_name'] ?? null,
                        'surname' => $params['surname'] ?? null,
                        'network' => $params['network'],
                        'msisdn_hash' => $this->GetMpesaMSISDNHash($params['msisdn']),
                        'status' => 1,
                        'created_at' => $this->base->now()]),
                    'state' => 'NEW',
                    'first_name' => $params['first_name'] ?? null,
                    'middle_name' => $params['middle_name'] ?? null,
                    'surname' => $params['surname'] ?? null,];
            }

            $updateSql = ['id' => $profile['id']];
            $sql = "UPDATE profile SET updated_at=now()";
            if (!$profile['first_name'] || $profile['first_name'] == '') {
                if (isset($params['first_name'])) {
                    $sql .= ",first_name=:first_name";
                    $updateSql[':first_name'] = $params['first_name'];
                }
            }

            if (!$profile['middle_name'] || $profile['middle_name'] == '') {
                if (isset($params['middle_name'])) {
                    $sql .= ",middle_name=:middle_name";
                    $updateSql[':middle_name'] = $params['middle_name'];
                }
            }

            if (!$profile['surname'] || $profile['surname'] == '') {
                if (isset($params['surname'])) {
                    $sql .= ",surname=:surname";
                    $updateSql[':surname'] = $params['surname'];
                }
            }

            if (!$profile['msisdn_hash']) {
                $sql .= ",msisdn_hash=:msisdn_hash";
                $updateSql[':msisdn_hash'] = $this->GetMpesaMSISDNHash($params['msisdn']);
            }

            $sql .= " WHERE id=:id LIMIT 1";

            $this->base->rawUpdateWithParams('dbWrite', $sql, $updateSql);

            $profile['state'] = 'EXISTS';

            return $profile;
        } catch (Exception $ex) {
            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "| " . $params['msisdn']
                    . "| Exception Code:" . $ex->getCode()
                    . "| Trace:" . $ex->getTraceAsString()
                    . "| Exception:" . $ex->getMessage());

            return false;
        }
    }

    /**
     * PaymentRouter
     * @param type $param
     * @return type
     */
    public function PaymentRouter($param) {
        $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                . "|PaymentRouter:" . json_encode($param));

        $dataArr = explode(':', $param['ThirdPartyTransID']);
        $groupId = $dataArr[0] ?? false;
        $productId = $dataArr[1] ?? false;
        $customerId = $dataArr[2] ?? false;

        $sql = "select cig.id,ip.name product_name,ig.name,c.msisdn,cp.first_name"
                . ",cp.middle_name,cp.surname,c.email_address,cig.total_assets "
                . "from customer_investment_group cig "
                . "join customer c on cig.cust_id=c.id "
                . "join investment_groups ig on cig.invest_group_id=ig.id "
                . "join customer_profile cp on c.profile_id=cp.id "
                . "join investment_products ip on cig.product_id=ip.id "
                . "join investment_product_settings ips on ip.id=ips.product_id "
                . "where cig.product_id=:product_id and cig.invest_group_id=:invest_group_id "
                . "and cig.cust_id=:cust_id and c.status=1 and cp.status=1";

        $res = $this->base->rawSelectOneRecord("dbWrite", $sql,
                [':invest_group_id' => $groupId,
                    ':cust_id' => $customerId,
                    ':product_id' => $productId]);
        if (!$res) {
            return [
                'code' => 404,
                'message' => 'Group Investment Settings NOT found!'
            ];
        }

        $utils = new Utilities();
        $transId = $utils->CreateTransaction(
                ['created_at' => $this->base->now(),
                    'cust_id' => $customerId,
                    'reference_id' => $param['paymentReferenceID'],
                    'reference_type_id' => 1,
                    'transaction_type_id' => TRANSACTION_TYPE_CREDIT_ID, #Credit
                    'amount' => $param['TransAmount'],
                    'currency' => $param['Currency'],
                    'source' => 'PREMIUM_PURCHASE',
                    'description' => "Premium purchase for "
                    . $res['product_name'] . ". TrxnId:" . $param['TransID'],
                    'extra_data' => json_encode([
                        'msisdn' => $param['MSISDN'],
                        "ticketId" => $productId,
                        'trx_id' => $param['TransID'],
                        'selection' => $res['name']])]);

        $this->base->rawUpdateWithParams("dbWrite",
                "UPDATE customer_investment_group SET total_assets=total_assets+:assets "
                . "WHERE id=:id LIMIT 1",
                [':assets' => $param['TransAmount'], ':id' => $res['id']]);

        $this->base->rawInsertBulk('dbWrite',
                'investment_group_payments',
                ['group_id' => $groupId,
                    'product_id' => $productId,
                    'amount' => $param['TransAmount'],
                    'transaction_id' => $transId,
                    'created_at' => $this->base->now()]);

        $ph = [
            '{Fname}' => $param['FirstName'],
            '{tNo}' => $transId,
            '{currency}' => 'KES',
            '{amount}' => $param['TransAmount'],
            '{Gname}' => $res['name'],
            '{Pname}' => $res['product_name'],
            '{trxnId}' => $param['TransID'],
            '{total_assets}' => $res['total_assets'] + $param['TransAmount'],
            '{helpline}' => $this->base->settings['HelpLine']
        ];

        $start = $this->base->getMicrotime();
        $msgUtils = new Messaging();
        $smsResult = $msgUtils->SendSMs(
                ['msisdn' => $param['MSISDN'],
                    'short_code' => 'LidenCo',
                    'message' => $this->base->SMSTemplate($this
                            ->base->settings['Message']['purchase'], $ph),
                    'unique_id' => $param['paymentReferenceID']]);
        $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                . "|" . $this->base->CalculateTAT($start) . " Sec(s)"
                . "| BusinessShortCode:" . $param['BusinessShortCode']
                . "| Account:" . $param['BillRefNumber']
                . "| paymentReferenceID:" . $param['paymentReferenceID']
                . "| SendSMs()" . json_encode($smsResult));

        return [
            'code' => 200,
            'message' => 'Premium Round Created Successfully!',
            'data' => [
                'transaction_id' => $transId
            ]
        ];
    }

    /**
     * CreateTransaction
     * @param type $transaction
     * @return type
     * @throws Exception
     */
    public function CreateTransaction($transaction) {
        try {
            return $this->base->rawInsertBulk('dbWrite', 'transaction', $transaction);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * CalculateExciseTax
     * @param type $stakeAmount
     * @param type $taxValue
     * @return array
     */
    public function CalculateExciseTax($stakeAmount, $taxValue): array {
        $stakeTax = $stakeAmount - (($stakeAmount * 100) / (100 + $taxValue));

        return [
            'stakeTax' => (Double) round($stakeTax, 2),
            'stakeAfterTax' => (Double) round(($stakeAmount - $stakeTax), 2)];
    }

    /**
     * CalculateWitholdingTax
     * @param type $amountWon
     * @param type $stakeAmount
     * @param type $taxValue
     * @return array
     */
    public function CalculateWitholdingTax($amountWon, $stakeAmount, $taxValue): array {
        $possibleWin = ($amountWon - $stakeAmount);
        $possibleWinAfter = ($possibleWin - (($possibleWin * 100) / (100 + $taxValue)));

        return [
            'posibleWin' => (Double) round(($amountWon - $possibleWinAfter), 2),
            'witholdingTax' => (Double) round($possibleWinAfter, 2)];
    }

    /**
     * TaxStakeRemit
     * @param type $param
     * @return type
     */
    public function TaxStakeRemit($param) {
        $kra_tax_remit = [
            'bet_id' => $param['betId'],
            'bet_type' => 'NORMAL',
            'bet_amount' => $param['TransAmount'],
            "customer_id" => $param['profileID'],
            "mobile_number" => $param['MSISDN'],
            'bet_data' => [
                'bet_odds' => $param['odds'],
                'bet_desc' => 'Virtual Bet (' . $param['game'] . ')',
                'bet_date' => $this->base->now(),
                'bet_outcome_date' => date('Y-m-d H:i:s', strtotime($this->base->now() . ' + 1 day')),
                'wallet_balance' => 0
            ],
            'timestamp' => time()];

        $queue = new Queue();
        return$queue->ConnectAndPublishToQueue($kra_tax_remit,
                        'MOSSBETS_STAKEREMIT',
                        'MOSSBETS_STAKEREMIT',
                        'MOSSBETS_STAKEREMIT');
    }

    /**
     * TaxResultsRemit
     * @param type $param
     * @return type
     */
    public function TaxResultsRemit($param) {
        $kra_tax_remit = [
            'bet_id' => $param['betId'],
            'bet_type' => 'NORMAL',
            'bet_amount' => $param['betAmount'],
            "win_amount" => $param['payoutAmount'],
            'outcome' => $param['outcome'],
            "outcome_date" => $this->base->now(),
            'wallet_balance' => $param['payoutAmount'],
            'timestamp' => time()];

        $queue = new Queue();
        return$queue->ConnectAndPublishToQueue($kra_tax_remit,
                        'MOSSBETS_TAX',
                        'MOSSBETS_TAX',
                        'MOSSBETS_TAX');
    }

    /**
     * PromoMechanics
     * @param type $param
     * @return type
     */
    public function PromoMechanics($param) {
        try {
            $mechanics = $this->base->rawSelect('dbWrite',
                    'select draw_mechanics.id,draw_mechanics.mechanic_name,draw_mechanics.game_id'
                    . ',games.name game_name,draw_mechanics.amount,draw_mechanics.hourly_limits'
                    . ',draw_mechanics.daily_limits,draw_mechanics.entry_no from draw_mechanics '
                    . 'join games on draw_mechanics.game_id=games.id '
                    . 'where draw_mechanics.status=1 '
                    . 'and games.status=1 and game_id=:game_id and stake=:stake '
//                    . 'and draw_mechanics.id not in (select mechanic_id '
//                    . 'from draw_mechanic_winners where profile_id=:profile_id '
//                    . 'and date(created_at)=date(now())) '
                    . 'ORDER BY RAND()',
                    [':game_id' => $param['game_id'],
                        // ':profile_id' => $param['profile_id'],
                        ':stake' => $param['amount']
            ]);
            if (!$mechanics) {
                return false;
            }

            $state = false;
            $referenceType = $this->settings['Account']['Games']['ReferenceTypeId'][$param['game_id']];
            foreach ($mechanics as $mechanic) {
                $dailyLimit = $this->base->rawSelectOneRecord('dbWrite',
                        'select ifnull(count(id),0)trx_count from draw_mechanic_winners '
                        . 'where date(created_at)=date(now()) and mechanic_id=:mechanic_id',
                        [':mechanic_id' => $mechanic['id']]);
                $dlimit = $dailyLimit['trx_count'] ?? 0;
                if ($dlimit >= $mechanic['daily_limits']) {
                    continue;
                }

                $hourLimit = $this->base->rawSelectOneRecord('dbWrite',
                        'select ifnull(count(id),0)trx_count from draw_mechanic_winners '
                        . 'where date(created_at)=date(now()) and hour(created_at)=hour(now()) '
                        . 'and mechanic_id=:mechanic_id',
                        [':mechanic_id' => $mechanic['id']]);
                $hlimit = $hourLimit['trx_count'] ?? 0;
                if ($hlimit >= $mechanic['hourly_limits']) {
                    continue;
                }

                $cmpVar = (int) 0;
                $cmpVar += (($mechanic['entry_no'] -
                        ($param['transaction_id'] % $mechanic['entry_no'])) % $mechanic['entry_no']);

                $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                        . "|MId:" . $mechanic['id']
                        . "|Amount:" . $mechanic['amount']
                        . "|EntryNo:" . $mechanic['entry_no']
                        . "|TrxnId::" . $param['transaction_id']
                        . "|Cmp Var <<>>$cmpVar");

                if ($cmpVar != 0) {
                    continue; // Entry Level Request
                }

                $id = $this->base->rawInsertBulk('dbWrite',
                        'draw_mechanic_winners',
                        ['created_at' => $this->base->now(),
                            'profile_id' => $param['profile_id'],
                            'mechanic_id' => $mechanic['id'],
                            'transaction_id' => $param['transaction_id'],
                            'amount' => $mechanic['amount'],
                            'status' => 1]);

                $state = [
                    'amount' => $mechanic['amount'],
                    'name' => $mechanic['mechanic_name'],
                    'trxnId' => $id];

                break;
            }

            return $state;
        } catch (Exception $ex) {
            return false;
        }
    }

}
