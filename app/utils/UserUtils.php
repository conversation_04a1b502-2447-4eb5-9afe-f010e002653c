<?php

use <PERSON>alcon\Mvc\Controller;
use ControllerBase as base;

class UserUtils extends Controller
{

    /**
     * CheckSuperUserPermission
     * @param array $permissions
     * @return bool
     */
    public static function CheckSuperUserPermission(array $permissions): bool
    {
        if (in_array(1, $permissions)) {
            return true;
        }

        return false;
    }

    /**
     * ValidateUserPermission
     * @param int $userId
     * @param string $permissionName
     * @param int $permissionId
     * @return bool
     */
    public static function ValidateUserPermission(int $userId, string $permissionName = null,
                                                  int $permissionId = null): bool
    {
        $base = new base();
        try {
//            $suRole = 'Super Administrator';
//            $sUser = $base->rawSelectOneRecord('dbUser',
//                "SELECT id FROM user_roles WHERE name=:name "
//                . "AND status=:status", [":status" => 1, ":name" => $suRole]);
//            if (!$sUser) {
//                $permission_arr = $base->rawSelectOneRecord("dbUser",
//                    "SELECT group_concat(id separator ':') as acl "
//                    . "FROM user_permissions WHERE status=1");
//                if ($permission_arr) {
//                    $sUser['id'] = $base->rawInsertBulk('dbUser',
//                        "user_roles",
//                        ['name' => $suRole,
//                            'description' => $suRole,
//                            'status' => 1,
//                            'permissions_acl' => $permission_arr['acl'],
//                            'created_at' => $base->now()]);
//                }
//            }

            $sUser = $base->rawSelectOneRecord(
                'dbUser',
                "SELECT id FROM user_roles WHERE id IN (1, 2) AND status = :status",
                [":status" => 1]
            );

            if (!$sUser) {
                $permission_arr = $base->rawSelectOneRecord(
                    "dbUser",
                    "SELECT GROUP_CONCAT(id SEPARATOR ':') AS acl FROM user_permissions WHERE status = 1");

                if ($permission_arr) {
                    // Create a default role (role_id 1) if none exists
                    $sUser['id'] = $base->rawInsertBulk(
                        'dbUser',
                        "user_roles",
                        [
                            'id' => 1, // explicitly set role_id if needed
                            'name' => 'Admin',
                            'description' => 'Administrator Role',
                            'status' => 1,
                            'permissions_acl' => $permission_arr['acl'],
                            'created_at' => $base->now()
                        ]
                    );
                }
            }

            $user = $base->rawSelectOneRecord('dbUser',
                "SELECT permission_acl,role_id FROM user_login WHERE user_id=:user_id "
                . "AND status=:status", [":status" => 1, ":user_id" => $userId]);
            if ($user) {

                $sql = "SELECT id FROM user_permissions WHERE name=:name ";
                $params = [':name' => $permissionName];
                $permissions = $base->rawSelectOneRecord('dbUser', $sql, $params);

                if (!$permissions) {
                    // Only allow role_id 1 or 2 to auto-create permissions
                    if (in_array($user['role_id'], [1, 2])) {
                        $permissions = [
                            'id' => $base->rawInsertBulk('dbUser',
                                "user_permissions",
                                ['user_id' => $userId,
                                    'name' => $permissionName,
                                    'description' => $permissionName,
                                    'status' => 1,
                                    'created_at' => $base->now()])
                        ];

                        // Auto-update permissions for role_id 1 and 2
                        $superAdminRoles = $base->rawSelect("dbUser",
                            "SELECT id FROM user_roles WHERE id IN (1, 2) AND status=1");

                        if ($superAdminRoles) {
                            $permission_arr = $base->rawSelectOneRecord("dbUser",
                                "SELECT group_concat(id separator ':') as acl "
                                . "FROM user_permissions WHERE status=1");

                            if ($permission_arr) {
                                foreach ($superAdminRoles as $role) {
                                    $base->rawUpdateWithParams('dbUser',
                                        "UPDATE user_roles SET permissions_acl=:permissions_acl "
                                        . "WHERE id=:id LIMIT 1", [':id' => $role['id'],
                                            ':permissions_acl' => $permission_arr['acl']]);
                                }
                            }
                        }
                    } else {
                        // For non-super admin users, return false if permission doesn't exist
                        return false;
                    }
                }

                // Super admin roles (1 and 2) bypass all permission checks
                if (in_array($user['role_id'], [1, 2])) {
                    return true;
                }

                $list = explode(":", $user['permission_acl']);
//                $list = explode(',', $user['permission_acl']); // or $list[0] if it's inside an array

                if ($permissionName) {
                    if ($permissions) {
                        if (count($list) > 0) {
                            if (in_array($permissions['id'], $list)) {
                                return true;
                            }
                        }
                    }
                }

                if (is_array($list)) {
                    $permissionId = json_encode((int)$permissions['id']);
                    if (in_array($permissionId, $list)) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * GetUserPermissions
     * @param type $permissions
     * @return type
     * @throws Exception
     */
    public static function GetUserPermissions($permissions)
    {
        $base = new base();
        try {
            if ($permissions == "" || $permissions == null) {
                return false;
            }
            return $base->rawSelect('dbUser',
                'select id,name from user_permissions where status=1 '
                . 'and id IN (' . $permissions . ') ');
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * LogUserActivity
     * @param type $params
     * @return type
     * @throws Exception
     */
    public static function LogUserActivity($params)
    {
        $base = new base();
        try {
            return $base->rawInsertBulk('dbUser',
                'user_logs',
                ['user_id' => $params['user_id'],
                    'activity' => $params['activity'],
                    'request' => is_string($params['request']) ?
                        $params['request'] :
                        json_encode($params['request']),
                    'created_at' => $base->now()]);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * HashCreateSignature
     * @param type $request
     * @param type $tokenKey
     * @return type
     */
    public static function HashCreateSignature($request, $tokenKey)
    {
        $request = (array)$request;
        $hashkey = "";
        ksort($request, SORT_ASC);
        foreach ($request as $key => $value) {
            if (is_array($value) || is_object($value)) {
                $value = (array)$value;
                ksort($value, SORT_ASC);
                foreach ($value as $keyVal => $val) {
                    if (is_array($val) || is_object($val)) {
                        $val = (array)$val;
                        ksort($val, SORT_ASC);
                        $key = [];
                        foreach ($val as $kVal => $vl) {
                            if (is_array($vl) || is_object($vl)) {
                                $vl = (array)$vl;
                                ksort($vl, SORT_ASC);
                                $key[$kVal] = $vl;
                                continue;
                            }
                            $key[$kVal] = $vl;
                        }
                        $val = $key;
                    }
                    $hashkey .= "&$keyVal=" . md5(json_encode($val));
                }
                continue;
            }

            $hashkey .= "&$key=$value";
        }

        return md5(substr($hashkey, 1) . '' . $tokenKey);
    }

    /**
     * HashCalculate
     * @param type $request
     * @param type $hashKey
     * @param type $tokenKey
     * @return bool
     */
    public static function HashCalculate($request, $hashKey, $tokenKey): bool
    {
        return true;

        if ((self::HashCreateSignature($request, $tokenKey)) == $hashKey) {
            return true;
        }
        return false;
    }

    /**
     * CheckBlackListAccount
     * @param type $userId
     * @return boolean
     * @throws Exception
     */
    public static function CheckBlackListAccount($userId)
    {
        $base = new base();
        try {
            $result = $base->rawSelectOneRecord('dbUser',
                "SELECT id,status,blacklist_reason FROM user_black_list "
                . "WHERE user_id=:user_id", [':user_id' => $userId]);
            if ($result) {
                if ($result['status'] == 3) {
                    return true;
                }
            }

            return false;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * OutboundRequest
     * @param type $authkey
     * @return boolean
     */
    public static function OutboundRequest(string $authkey): bool
    {
        $base = new base();
        try {
            $sysKey = (string)$base->settings['Authentication']['ApplicationKey'];
            if ($authkey !== $sysKey) {
                return false;
            }

            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * GetApplicationKeys
     * @param type $key
     * @return boolean
     */
    public static function GetApplicationKeys($key)
    {
        $base = new base();
        try {
            $results = $base->rawSelectOneRecord('dbUser',
                "SELECT id channelId,name channelName FROM auth_channels "
                . "WHERE app_key=:app_key AND status=1", [':app_key' => $key]);
            if (!$results) {
                return false;
            }

            return $results;
        } catch (Exception $ex) {
            $base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . " | AppKey [$key]"
                . " | Trace:" . $ex->getTraceAsString()
                . " | Message::" . $ex->getMessage());
            return false;
        }
    }

    /**
     * QuickAuthenticate
     * @param type $apiTokenKey
     * @return type
     */
    public static function QuickAuthenticate($apiTokenKey)
    {
        $base = new base();
        $start = $base->getMicrotime();

        try {
            $userInfo = $base->rawSelectOneRecord('dbUser',
                'SELECT user_login.user_id,user.user_name,user.type,user_login.role_id'
                . ',user_login.permission_acl,user_login.status FROM user_login '
                . 'JOIN user ON user_login.user_id=user.id '
                . 'WHERE user_login.access_token=:access_token '
                . 'AND user_login.failed_attempts=0 AND user_login.status=1 '
                . 'AND user.status=1 AND user_login.blocked_timeline IS NULL',
                [':access_token' => $apiTokenKey]);
            if (!$userInfo) {
                return ['code' => 401, 'message' => 'Authentication failure. Invalid access key!'];
            }

            if ($userInfo['status'] != 1) {
                return ['code' => 401, 'message' => 'User is in an inactive state!'];
            }

            $accessTokenInfo = RedisUtils::redisRawSelectData($base->settings['ServerName']
                . "#AuthKey#$" . $userInfo['user_id']);
            $accessToken = $accessTokenInfo->api_key ?? false;
            if (!$accessToken) {
                return ['code' => 401, 'message' => 'Authentication failure. Access is forbidden!'];
            }

            $userData = new stdClass();
            $userData = json_decode($base->Decrypt($accessToken));
            $now = new DateTimeImmutable();
            $timestamp = $now->getTimestamp();

            if ((!$userData->iss) ||
                ($userData->exp < $timestamp) ||
                ($userData->nbf > $timestamp) ||
                ($userData->st != 1)) {
                return ['code' => 401, 'message' => 'Authentication failure. Expired token!'];
            }

            $ipAddress = $userData->ip ?? false;
            if (!$ipAddress) {
                return ['code' => 401, 'message' => 'Authentication failure. Session expired!'];
            }

            // Get additional user details based on user type
            $additionalData = [];
            if ($userInfo['type'] === 'Partner') {
                // Get partner details if user is a partner
                $partnerDetails = $base->rawSelect('dbUser',
                    "SELECT id, name, email_address, address, country, dial_code, msisdn, status, created_at
                     FROM partners WHERE user_id = :user_id AND status = 1 ORDER BY name ASC",
                    [':user_id' => $userInfo['user_id']]);

                $partnerCount = $base->rawSelectOneRecord('dbUser',
                    "SELECT COUNT(id) as partner_count FROM partners WHERE user_id = :user_id AND status = 1",
                    [':user_id' => $userInfo['user_id']]);

                $additionalData['partner_count'] = $partnerCount['partner_count'] ?? 0;
                $additionalData['partners'] = $partnerDetails ?? [];

                // Extract partner IDs for easy access
                $partnerIds = [];
                if ($partnerDetails) {
                    foreach ($partnerDetails as $partner) {
                        $partnerIds[] = $partner['id'];
                    }
                }
                $additionalData['partner_ids'] = $partnerIds;
            } else {
                // For non-partner users, set empty partner data
                $additionalData['partner_count'] = 0;
                $additionalData['partners'] = [];
                $additionalData['partner_ids'] = [];
            }

            return [
                'code' => 200,
                'message' => 'User authenticated successfully.',
                'data' => array_merge([
                    'user_id' => $userData->uid,
                    'user_name' => $userInfo['user_name'],
                    'user_type' => $userInfo['type'],
                    'role_id' => $userInfo['role_id'],
                    'permission_acl' => $userInfo['permission_acl'],
                ], $additionalData)
            ];
        } catch (Exception $ex) {
            $base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . " | Decryption Failed for TokenKey [$apiTokenKey]"
                . " | Trace:" . $ex->getTraceAsString()
                . " | Message::" . $ex->getMessage());
            return ['code' => 401, 'message' => 'Authentication Failure!. Reason:' . $ex->getMessage()];
        }
    }


}
