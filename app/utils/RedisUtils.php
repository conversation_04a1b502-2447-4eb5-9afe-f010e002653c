<?php

use <PERSON>alcon\Mvc\Controller;
use ControllerBase as base;

class RedisUtils extends Controller {

    /**
     * SetTimeoutInSeconds
     * @param type $time
     * @return type
     */
    public static function SetTimeoutInSeconds($time) {
        return $time * 60;
    }

    /**
     * redisRawSelectData
     * @param type $key
     * @return boolean
     * @throws Exception
     */
    public static function redisRawSelectData($key) {
        $base = new base();

        try {
            $connection = $base->di->getShared('redis');
            if ($connection !== false) {
                $data = $connection->get($base->settings['systemName'] . '_' . $key);

                $connection = null;
                if ($data) {
                    return json_decode($data);
                }
            }

            return false;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * redisRawInsertData
     * @param string $key
     * @param type $data
     * @param type $timeout
     * @return \stdClass
     * @throws Exception
     */
    public static function redisRawInsertData($key, $data, $timeout = false) {
        $result = new \stdClass();
        $base = new base();

        $key = $base->settings['systemName'] . '_' . $key;
        try {
            $connection = $base->di->getShared('redis');

            $data = json_encode($data);
            if ($connection !== false) {
                if (is_numeric($timeout)) {
                    $connection->setex($key, $timeout, $data);
                } else {
                    $connection->set($key, $data);
                }

                $connection->bgSave();
                $connection = null;

                $result->status = true;
                $result->description = "Set '$key' successfully";
                return $result;
            }

            $result->status = false;
            $result->description = "Connection to Redis Server failed";
            return $result;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * redisRawDelete
     * @param string $key
     * @return boolean
     * @throws Exception
     */
    public static function redisRawDelete($key,$allMatches = false) {
        $base = new base();

        $key = $base->settings['systemName'] . '_' . $key;

        if($allMatches){
            $key = $base->settings['systemName'] . '_' . $key . '*';
        }

        try {
            $connection = $base->di->getShared('redis');
            if ($connection !== false) {
                if($allMatches){
                    $keys = $connection->keys($key);
                    if($keys){
                        foreach($keys as $k){
                            $connection->del($k);
                        }
                    }

                    return true;
                }
                return $connection->del($key);
            }

            return false;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    /**
     * Invalidate partner-related cache keys
     * @param int $partnerId
     * @return boolean
     */
    public static function invalidatePartnerCache($partnerId) {
        try {
            // Invalidate all partner-related caches
            self::redisRawDelete("partners_list", true);
            self::redisRawDelete("partner_services_$partnerId", true);
            self::redisRawDelete("partner_settings_$partnerId", true);
            self::redisRawDelete("partner_bets_$partnerId", true);
            self::redisRawDelete("partner_bet_slips_$partnerId", true);

            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * Invalidate partner services cache
     * @param int $partnerId
     * @param int $serviceId
     * @return boolean
     */
    public static function invalidatePartnerServicesCache($partnerId, $serviceId = null) {
        try {
            self::redisRawDelete("partner_services_$partnerId", true);
            if ($serviceId) {
                self::redisRawDelete("partner_service_$serviceId");
            }
            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * Invalidate partner settings cache
     * @param int $partnerId
     * @param int $settingsId
     * @return boolean
     */
    public static function invalidatePartnerSettingsCache($partnerId, $settingsId = null) {
        try {
            self::redisRawDelete("partner_settings_$partnerId", true);
            if ($settingsId) {
                self::redisRawDelete("partner_setting_$settingsId");
            }
            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * Invalidate partner bets cache
     * @param int $partnerId
     * @param int $betId
     * @return boolean
     */
    public static function invalidatePartnerBetsCache($partnerId, $betId = null) {
        try {
            self::redisRawDelete("partner_bets_$partnerId", true);
            if ($betId) {
                self::redisRawDelete("partner_bet_$betId");
                // Also invalidate related bet slips
                self::redisRawDelete("partner_bet_slips_bet_$betId", true);
            }
            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * Invalidate partner bet slips cache
     * @param int $partnerId
     * @param int $slipId
     * @return boolean
     */
    public static function invalidatePartnerBetSlipsCache($partnerId, $slipId = null) {
        try {
            self::redisRawDelete("partner_bet_slips_$partnerId", true);
            if ($slipId) {
                self::redisRawDelete("partner_bet_slip_$slipId");
            }
            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    /**
     * Generate cache key for partner data
     * @param string $type
     * @param array $params
     * @return string
     */
    public static function generateCacheKey($type, $params = []) {
        $base = new base();
        $keyParts = [$type];

        foreach ($params as $key => $value) {
            if ($value !== false && $value !== null && $value !== '') {
                $keyParts[] = $key . '_' . $value;
            }
        }

        return implode('_', $keyParts);
    }

}
