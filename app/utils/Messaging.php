<?php

use Phalcon\Mvc\Controller;
use ControllerBase as base;

class Messaging extends Controller {

    protected $base;

    function onConstruct() {
        $this->base = new base();
    }

    /**
     * SendEmail
     * @param array $from
     * @param array $recievers
     * @param string $subject
     * @param string $body
     * @param array $email_bcc
     * @param array $email_cc
     * @return array
     */
    public function SendEmail(array $from, array $recievers, string $subject, string $body,
            array $email_bcc = null, array $email_cc = null): array {
        $base = new base();
        try {
            require_once (__DIR__) . '/../../vendor/swiftmailer/swiftmailer/lib/swift_required.php';

            $host_ip = gethostbyname($base->mail['Host']);
            $transport = \Swift_SmtpTransport::newInstance($base->mail['Host'],
                            587, 'tls')
                    ->setUsername($base->mail['Sender'])
                    ->setPassword($base->mail['Pass']);

            if (!$from) {
                $from = [
                    'from' => $this->base->mail['AdminSender'],
                    'decription' => 'MossBets BackOffice'];
            }

            $mailer = \Swift_Mailer::newInstance($transport);
            $message = \Swift_Message::newInstance($subject)
                    ->setFrom(array($from['from'] => $from['decription']));

            $recievers = array_unique($recievers);
            foreach ($recievers as $reciever) {
                $recievers[$reciever] = $reciever;
            }

            if (count($recievers) < 1) {
                return [
                    'code' => 400,
                    'message' => 'Email not sent!'
                ];
            }

            $message->setTo($recievers);

            if ($email_bcc) {
                $email_bcc = array_unique($email_bcc);
                foreach ($email_bcc as $bcc) {
                    $email_bcc[$bcc] = $bcc;
                }

                if (count($email_bcc)) {
                    $message->setBcc($email_bcc);
                }
            }

            if ($email_cc) {
                $email_cc = array_unique($email_cc);
                foreach ($email_cc as $cc) {
                    $email_cc[$cc] = $cc;
                }

                if (count($email_cc)) {
                    $message->setCc($email_cc);
                }
            }

            $message->setBody($body, 'text/html');
            $result = $mailer->send($message);

            if ($result > 0) {
                return [
                    'code' => 200,
                    'message' => 'Email email without attachment sent!!',
                    'response' => $result];
            }

            return [
                'code' => 400,
                'message' => 'Email not sent!'];
        } catch (Exception $ex) {
            return [
                'code' => 500,
                'message' => 'Email not sent. Exception:' . $ex->getMessage()
            ];
        }




        // settings from config
        $smtp = $this->mail['Host'];
        $smtpPort = $base->mail['PortNumber'];
        $username = $base->mail['Sender'];
        $password = $base->mail['Pass'];
        $senderID = $base->mail['Sender'];


        $body = str_replace('{line}', "<br>", $body);
        $mail = new PHPMailer(true);

        try {
            $mail->Host = $smtp;
            $mail->SMTPAuth = true;
            $mail->Username = $username;
            $mail->Password = $password;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = 587;

            $mail->setFrom($username, 'KIRON LITE');
            $mail->addAddress($receiver, $name);
            $mail->addReplyTo($senderID, 'KIRON LITE');

            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;

            $res = $mail->send();
            $this->infologger->info(__LINE__ . ':' . __CLASS__ . "|" . json_encode($res));

            return true;
        } catch (Exception $e) {
            $this->getLogFile('error')->emergency(__LINE__ . ':' . __CLASS__
                    . "| Exception"
                    . "| Trace::" . $e->getTraceAsString()
                    . "| Message::" . $e->getMessage());
            return false;
        }
    }

    /**
     * 
     * @param type $params
     */
    function SendBulkSMS($params) {
        $start = $this->base->getMicrotime();
        $params['message'] = $this->base
                ->SMSTemplate($params['message'], ['{line}' => "\n"]);

        $sms_pages = 1;
        if (strlen($params['message']) > 160) {
            $sms_pages = ceil(strlen($params['message']) / 160);
        }

        try {
            $recipients = $this->base->rawSelect('dbWrite',
                    "select distinct profile.msisdn,profile.first_name,',,' "
                    . "from game_transactions join profile on game_transactions.profile_id=profile.id "
                    . "where game_transactions.game_round_id=:game_round_id and profile.status=1",
                    [":game_round_id" => $params['game_round_id']]);
            if (!$recipients) {
                return [
                    'code' => 204,
                    'message' => 'Query returned an empty Contact list(s)!'];
            }

            $fileName = "FINAL_DATA_RASHARASHA_" . rand(1000, 99999) . ".csv";
            $filePath = $this->settings['uploadDir']['Sms'] . "" . $fileName;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $file = fopen($filePath, 'w');

            $csv_row = [];
            $csv_row[] = explode(',', $this->settings['uploadDir']['SmsHeaders']);

            $recipients[] = ["254704050143", 'Josephat', '', '',];
            $recipients[] = ["254725547995", 'Daniel', '', '',];
            header('Content-Type: text/csv; charset=utf-8');

            foreach ($csv_row as $line) {
                fputcsv($file, $line);

                foreach ($recipients as $value) {
                    fputcsv($file, $value);
                }
            }

            $correlator = $this->base->rawInsertBulk('dbWrite',
                    'game_round_bulk',
                    ['created_at' => $this->base->now(),
                        'game_round_id' => $params['game_round_id'],
                        'message' => $params['message'],
                        'recipients' => count($recipients) - 1,
                        'sms_pages' => $sms_pages,
                        'type' => $params['type'],
                        'status' => $params['status']]);

            $fileType = mime_content_type($filePath);

            $token = $this->GetLidenAccessToken();
            if (!$token) {
                $updateSql = "UPDATE game_round_bulk SET status=:status,completed_on=NOW() "
                        . "WHERE id=:id LIMIT 1";

                $this->base->rawUpdateWithParams('dbWrite', $updateSql,
                        [':id' => $correlator,
                            ':status' => 100]);

                unlink($filePath);

                return [
                    'code' => 100,
                    'message' => 'Authentication process failed!'];
            }

            $isScheduled = 0;
            $ScheduleDate = '';
            $ScheduleTime = '';
            $shortCode = $this->base->settings['mnoApps']['DefaultSenderId'];
            $url = $this->settings['mnoApps']['Urls']['BulkBroadcastUrl'];

            $header = ['Content-Type: multipart/form-data',
                'X-Authorization-Key:' . $token,
                'X-Requested-With:XMLHttpRequest'];

            $postData = new stdClass();
            $postData->shortCode = "$shortCode";
            $postData->message = $params['message'];
            $postData->isScheduled = "$isScheduled";
            $postData->scheduleDate = "$ScheduleDate";
            $postData->scheduleTime = "$ScheduleTime";
            $postData->approval = "";
            $postData->uniqueId = $correlator;
            $postData->callbackURL = '';

            $postData->file = new CurlFile($filePath, $fileType, $fileName);

            $httpRequest = curl_init();
            curl_setopt($httpRequest, CURLOPT_URL, $url);
            curl_setopt($httpRequest, CURLOPT_POST, true);
            curl_setopt($httpRequest, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $header);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, true);
            $results = curl_exec($httpRequest);
            $status = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
            curl_close($httpRequest);

            $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                    . " | Unique_id:$correlator"
                    . " | $shortCode"
                    . " | [$url]"
                    . " | Request:" . json_encode($postData)
                    . " | HttpCode:$status"
                    . " | Result:$results");

            $serverResponse = json_decode($results);
            $code = $serverResponse->code ?? 'Error';
            $messageBack = $serverResponse->statusDescription
                    . ". Reason:" . ($serverResponse->data->message ?? "Request Failed");

            $updateParams = [
                ':id' => $correlator,
                ':status' => $status];
            $updateSql = "UPDATE game_round_bulk SET status=:status,completed_on=NOW()";
            if ($status == 200 && $code == 'Success') {
                $updateSql .= ",campaign_id=:campaign_id";
                $updateParams[':campaign_id'] = $serverResponse->data->data->sms_data->campaign_id;
            }

            $updateSql .= " WHERE id=:id LIMIT 1";

            $this->base->rawUpdateWithParams('dbWrite', $updateSql, $updateParams);
            unlink($filePath);

            return [
                'code' => $status,
                'message' => $messageBack];
        } catch (Exception $ex) {
            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "|" . $this->base->CalculateTAT($start) . " Sec(s)"
                    . "| Exception Trace:" . $ex->getTraceAsString()
                    . "| Message:" . $ex->getMessage());

            return [
                'code' => 500,
                'message' => "Exception Message:" . $ex->getMessage()];
        }
    }

    /**
     * SendSMs
     * @param type $params
     * @return type
     */
    function SendSMs($params) {
        $start = $this->base->getMicrotime();
        try {
            $params['message'] = $this->base->SMSTemplate($params['message'],
                    ['{line}' => "\n",
                        "{helpline}" => $this->base->settings['Helpline']]);

            $sms_pages = 1;
            if (strlen($params['message']) > 160) {
                $sms_pages = ceil(strlen($params['message']) / 160);
            }

            $correlator = $this->base->rawInsertBulk('dbUser',
                    'user_outbox',
                    ['created_at' => $this->base->now(),
                        'user_id' => $params['user_id'],
                        'message' => $params['message'],
                        'sender_id' => $sms_pages,
                        'message_type' => $params['message_type'],
                        'message_pages' => $sms_pages,
                        'status' => 5,
                        'unique_id' => $params['unique_id']]);

            $payload = [
                'campaign_id' => $params['unique_id'],
                'message_pages' => $sms_pages,
                'message' => $params['message'],
                'sms-id' => 'QUICKSENDVERIFICATION',
                'network_regex' => 1,
                'network' => 'SAFARICOM',
                'alert_type' => 'TRANSACTIONAL',
                'recipients' => $params['msisdn'],
                'outbox_id' => $correlator,
                'short_code' => 'MOSSBETS_TS',
                'gateway' => 1,
                'date_created' => $this->base->now(),
                'dlr_url' => '',
                'auth_token' => 'auth_token_api'];

            $start = $this->base->getMicrotime();
            $queue = new Queue();
            $res = $queue->ConnectAndPublishToQueue($payload, 'OUTBOX', 'OUTBOX', 'OUTBOX'
                    , null, null, null, null, "/", null);
            $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                    . "|" . $this->base->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $params['msisdn'] . "]"
                    . "| OutboxId::" . $params['unique_id']
                    . "| ConnectAndPublishToQueue()" . json_encode($res));

            $code = $res->code ?? false;
            if (!$code || ($code != 200)) {
                return [
                    'success' => false,
                    'correlator' => $correlator,
                    'message' => 'Send Sms failed. Try again sometime!'];
            }

            return [
                'success' => true,
                'correlator' => $correlator,
                'message' => 'Message has been queued for processing'];
        } catch (Exception $ex) {
            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . "|" . $this->base->CalculateTAT($start) . " Sec(s)"
                    . "| [" . $params['msisdn'] . "]"
                    . "| OutboxId::" . $params['unique_id']
                    . "| Exception Trace:" . $ex->getTraceAsString()
                    . "| Message:" . $ex->getMessage());

            return [
                'success' => false,
                'message' => 'Send Sms failed on exception. Try again sometime!'];
        }
    }

    /**
     * GetLidenAccessToken
     * @return boolean
     */
    function GetLidenAccessToken() {
        $start = $this->base->getMicrotime();

        $key = $this->base->settings['appName'] . "$" . __FUNCTION__ . "_AccessKey";
        try {
//            $data = RedisUtils::redisRawSelectData($key);
//            if ($data) {
//                return $data->access_token;
//            }
//
//            RedisUtils::redisRawDelete($key);

            $payload = [
                'apigw' => 'API_GW',
                'password' => "Keny@1-Nb1",
                'userName' => '704050143',
                'countryCode' => 254];

            $headers[] = 'Content-Length: ' . strlen(json_encode($payload));
            $headers[] = 'X-Requested-With: XMLHttpRequest';
            $headers[] = 'Content-Type: application/json';

            $httpRequest = curl_init($this->base->settings['mnoApps']['Urls']['BulkAuthUrl']);
            curl_setopt($httpRequest, CURLOPT_NOBODY, true);
            curl_setopt($httpRequest, CURLOPT_POST, true);
            curl_setopt($httpRequest, CURLOPT_POSTFIELDS, json_encode($payload));
            curl_setopt($httpRequest, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($httpRequest, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($httpRequest, CURLOPT_TIMEOUT, $this->base->settings['timeoutDuration']);
            curl_setopt($httpRequest, CURLOPT_CONNECTTIMEOUT, $this->base->settings['timeoutDuration']);
            curl_setopt($httpRequest, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
//accept SSL settings
            curl_setopt($httpRequest, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($httpRequest, CURLOPT_USERPWD, '<EMAIL>:' . md5('<EMAIL>'));
            curl_setopt($httpRequest, CURLOPT_USERAGENT, $this->base->settings['appName'] . "/3.0");

            $results['response'] = curl_exec($httpRequest);
            $results['statusCode'] = curl_getinfo($httpRequest, CURLINFO_HTTP_CODE);
            $results['curlError'] = curl_error($httpRequest);
            curl_close($httpRequest);

            $this->base->infologger->info(__LINE__ . ":" . __CLASS__
                    . "| SendHttpJsonPostData::GetLidenAccessToken()"
                    . "| Request:" . json_encode($payload)
                    . "| Response:" . json_encode($results));
            if ($results['statusCode'] != 200) {
                return false;
            }

            $response = json_decode($results['response']);
            $code = $response->code ?? false;
            if ($code != 'Success') {
                return false;
            }

//            RedisUtils::redisRawInsertData($key,
//                    ['access_token' => $response->data->data->token],
//                    ($response->data->data->expires * 86400));

            return $response->data->data->token;
        } catch (Exception $ex) {
            $this->base->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                    . " | Exception::" . $ex->getMessage());
            return false;
        }
    }

}
