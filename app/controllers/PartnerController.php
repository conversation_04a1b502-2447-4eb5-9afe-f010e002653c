<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * Description of PartnerController
 *
 * <AUTHOR>
 */
class PartnerController extends \ControllerBase
{

    /**
     * GetPartners
     * @return type
     */
    public function GetPartners()
    {
        $start_time = $this->getMicrotime();

        $permissionName = "View Partners";

        $data = $this->request->get();

        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartners :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_ids', 'search', 'name', 'limit', 'skip_cache', 'sort', 'page', 'export', 'status', 'start', 'end'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . " | Request GetPartners ___________-------------______---------__--_-_ :" . json_encode($authResult));


            // Build pagination parameters
            $pagination = ControllerHelpers::buildPaginationParams($data, $this->settings['SelectRecordLimit']);

            // Parse and validate sort parameters
            $sortParams = ControllerHelpers::parseSortParams($extractedData['sort'] ?: 'id|DESC', 'id', 'DESC', 'p.');

            // Validate skip cache parameter
            $skipCache = ControllerHelpers::validateSkipCache($extractedData['skip_cache']);

            // Extract date range
            $dateRange = ControllerHelpers::extractDateRange($extractedData);

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partners_list', [
                'name' => $extractedData['name'] ?? $extractedData['search'],
                'status' => $extractedData['status'],
                'start_date' => $dateRange['start_date'],
                'end_date' => $dateRange['end_date'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit'],
                'sort' => $sortParams['field'] . '_' . $sortParams['order']
            ]);

            // Check cache first (unless skip_cache is set)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partners successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];


            // use find in set
            if (!empty($extractedData['partner_ids']) && $extractedData['partner_ids'] !== 'all') {
                $partnerIds = array_map('trim', explode(',', $extractedData['partner_ids']));
                $partnerIds = array_filter($partnerIds, 'is_numeric'); // ensure only numbers

                if (!empty($partnerIds)) {
                    $findInSetConditions = [];
                    foreach ($partnerIds as $index => $id) {
                        $paramKey = ":partner_id_$index";
                        $findInSetConditions[] = "FIND_IN_SET($paramKey, p.id)";
                        $searchParams[$paramKey] = (int)$id;
                    }
                    $searchQuery .= " AND (" . implode(' OR ', $findInSetConditions) . ")";
                }
            }

            if ($extractedData['name']) {
                $searchQuery .= " AND p.name LIKE :name";
                $searchParams[':name'] = '%' . $extractedData['name'] . '%';
            }

            if (is_numeric($extractedData['status'])) {
                $searchParams[':status'] = $extractedData['status'];
                $searchQuery .= " AND p.status = :status";
            }

            if ($dateRange['start_date'] && $dateRange['end_date']) {
                $searchQuery .= " AND p.created_at BETWEEN :start_date AND :end_date ";
                $searchParams[':start_date'] = $dateRange['start_date'] . " 00:00:00";
                $searchParams[':end_date'] = $dateRange['end_date'] . " 23:59:59";
            } elseif ($dateRange['end_date']) {
                $searchQuery .= " AND p.created_at <= :end_date";
                $searchParams[':end_date'] = $dateRange['end_date'] . " 23:59:59";
            } elseif ($dateRange['start_date']) {
                $searchQuery .= " AND p.created_at >= :start_date";
                $searchParams[':start_date'] = $dateRange['start_date'] . " 00:00:00";
            }

            // Build sorting query
            $sorting = ControllerHelpers::buildSortingQuery($pagination, $sortParams['field'], $sortParams['order']);

            // Query
            $sql = "SELECT (SELECT COUNT(p.id) FROM partners p $searchQuery) as trx_count,
                    p.id, p.name, p.status, p.email_address, p.dial_code, p.msisdn, p.address, p.country, p.created_at as created, p.updated_at as updated
                    FROM partners p $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $sql, $searchParams);
//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . "| SQL:" . $sql
//                . "| Params:" . json_encode($searchParams)
//                . "| Results==>" . json_encode($results)
//            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    ['code' => 404,
                        'message' => 'Request returned no Partners!'], true);
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(15)); // 15 minutes cache
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                ['code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count']
                        . ' Partners successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'],
                        'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                ['code' => 500,
                    'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartner
     * @return type
     */
    public function CreatePartner()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partners";

        $data = $this->request->getJsonRawBody();

//        $this->infologger->info(__LINE__ . ":" . __CLASS__
//            . " | Request CreatePartner :" . json_encode($data)
//        );

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = [
                'timestamp',
                'partner_name', 'partner_email_address', 'partner_address', 'partner_country', 'partner_dial_code',
                'partner_msisdn',
                'user_name', 'user_type', 'display_name', 'msisdn', 'role_id',
                'settings_api_key', 'ip_address', 'callback_url', 'settings_currency', 'settings_denomination',
                'settings_timezone', 'settings_billing_mode', 'settings_rate_limit', 'settings_websites', 'settings_version',
                'service_id', 'services_rate_limit_per_minute',
            ];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $params['timestamp'];


            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . " | Request CreatePartner :" . json_encode($params));

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Check if partner name already exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE name = :name", [':name' => $params['partner_name']]);

            if ($existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner name already exists!'], true);
            }

            // Insert new partner
            $partnerId = $this->rawInsertBulk('dbUser', 'partners', [
                'name' => $params['partner_name'],
                'email_address' => $params['partner_email_address'],
                'address' => $params['partner_address'],
                'country' => $params['partner_country'],
                'msisdn' => $params['partner_msisdn'],
                'dial_code' => $params['partner_dial_code'],
                'status' => $params['partner_status'] ?? 1,
                'created_by' => $authResult['user_id'],
                'created_at' => $this->now(),
                'updated_at' => $this->now(),
            ]);

            if (!$partnerId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner!'], true);
            }

            // Add user then update partner with user_id
            $userId = $this->rawInsertBulk('dbUser', 'user', [
                'user_name' => $params['user_name'],
                'msisdn' => $params['msisdn'],
                'password' => $params['password'] ?? $this->randStrGen(6), // Will be encrypted later
                'display_name' => $params['display_name'],
                'partner_ids' => json_encode([$partnerId]), // as json array
                'type' => "Partner",
                'role_id' => $params['role_id'] ?? 3,
                'status' => 1,
                'created_at' => $this->now(),
                'created_by' => $authResult['user_id']
            ]);

            if (!$userId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create user!'], true);
            }

            $updateSql = "UPDATE partners SET user_id = :user_id WHERE id = :id";
            $updateParams = [':user_id' => $userId, ':id' => $partnerId];
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner with user id!'], true);
            }

            // partner settings then services
            $partnerSettingsId = $this->rawInsertBulk('dbUser', 'partner_settings', [
                'partner_id' => $partnerId,
                'api_key' => $this->Encrypt(md5($partnerId . "$" . time())),
                'ip_address' => $params['ip_address'] ?? null,
                'callback_url' => $params['callback_url'] ?? null,
                'currency' => $params['settings_currency'] ?? null,
                'denomination' => $params['settings_denomination'] ?? null,
                'timezone' => $params['settings_timezone'] ?? null,
                'billing_mode' => $params['settings_billing_mode'] ?? 'prepay',
                'rate_limit' => $params['settings_rate_limit'] ?? 50,
                'websites' => $params['settings_websites'] ?? null,
                'version' => 1,
                'updated_by' => $authResult['user_id'],
                'created_at' => $this->now(),
                'updated_at' => $this->now()
            ]);

            if (!$partnerSettingsId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner settings!'], true);
            }

            $partnerServiceId = $this->rawInsertBulk('dbUser', 'partner_services', [
                'partner_id' => $partnerId,
                'service_id' => $params['service_id'],
                'rate_limit_per_minute' => 60,
                'status' => 1,
                'created_at' => $this->now()
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner created successfully!',
                    'data' => ['partner_id' => $partnerId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartner
     * @return type
     */
    public function UpdatePartner($partnerId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partners";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartner : For Partner ID: " . $partnerId . " | " . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_user_id', 'name', 'status', 'address', 'country', 'msisdn', 'dial_code', 'email_address'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

//            $partnerId = $extractedData['partner_id'];
            $partnerUserId = $extractedData['partner_user_id'];
            $name = $extractedData['name'];
            $status = $extractedData['status'];
            $address = $extractedData['address'];
            $country = $extractedData['country'];
            $dialCode = $extractedData['dial_code'];
            $msisdn = $extractedData['msisdn'];
            $emailAddress = $extractedData['email_address'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            if (!$partnerId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => "Partner ID is required!"], true);
            }

            // Validate status if provided
            if ($status && !in_array($status, ['1', '0', '3'])) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => "Invalid status. Must be 'active' or 'inactive'"], true);
            }

            // Get authenticated user data
            $userData = $authResult['user_data'];

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id, name FROM partners WHERE id = :id", [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check if new name conflicts with existing partner (if name is being updated)
            if ($name && $name !== $existingPartner['name']) {
                $nameConflict = $this->rawSelectOneRecord('dbUser',
                    "SELECT id FROM partners WHERE name = :name AND id != :id",
                    [':name' => $name, ':id' => $partnerId]);

                if ($nameConflict) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                        200, 'Request is not successful',
                        ['code' => 409, 'message' => 'Partner name already exists!'], true);
                }
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerId];

            if ($name) {
                $updateFields[] = "name = :name";
                $updateParams[':name'] = $name;
            }
            if (is_numeric($status)) {
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }
            if ($address !== null) {
                $updateFields[] = "address = :address";
                $updateParams[':address'] = $address;
            }
            if ($country !== null) {
                $updateFields[] = "country = :country";
                $updateParams[':country'] = $country;
            }

            if ($dialCode !== null) {
                $updateFields[] = "dial_code = :dial_code";
                $updateParams[':dial_code'] = $dialCode;
            }
            if ($msisdn !== null) {
                $updateFields[] = "msisdn = :msisdn";
                $updateParams[':msisdn'] = $msisdn;
            }

            if ($emailAddress !== null) {
                $updateFields[] = "email_address = :email_address";
                $updateParams[':email_address'] = $emailAddress;
            }

//            if ($partnerUserId !== null) {
//                $updateFields[] = "user_id = :user_id";
//                $updateParams[':user_id'] = $partnerUserId;
//            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partners SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     *
     *  PartnerServices
     *
     **/

    /**
     * GetPartnerServices - Get services for a specific partner
     * @return type
     */
    function GetPartnerServices()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Services";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request GetPartnerServices:" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_ids', 'status', 'skip_cache'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'p.name', 'DESC');
//            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'ps.id', 'DESC');

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_services', [
                'partner_ids' => $extractedData['partner_ids'],
                'status' => $extractedData['status'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            $skipCache = $extractedData['skip_cache'] ?: 0;
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner services successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];

            if (!empty($extractedData['partner_ids']) && $extractedData['partner_ids'] !== 'all') {
                $partnerIds = array_map('trim', explode(',', $extractedData['partner_ids']));
                $partnerIds = array_filter($partnerIds, 'is_numeric'); // ensure only numbers

                if (!empty($partnerIds)) {
                    $findInSetConditions = [];
                    foreach ($partnerIds as $index => $id) {
                        $paramKey = ":partner_id_$index";
                        $findInSetConditions[] = "FIND_IN_SET($paramKey, ps.partner_id)";
                        $queryParams[$paramKey] = (int)$id;
                    }
                    $searchQuery .= " AND (" . implode(' OR ', $findInSetConditions) . ")";
                }
            }

            if (is_numeric($extractedData['status'] !== false)) {
                $searchQuery .= " AND ps.status = :status";
                $queryParams[':status'] = $extractedData['status'];
            }


            // Execute query
            $query = "SELECT (SELECT COUNT(ps.id) FROM partner_services ps $searchQuery) as service_count,
                      ps.id, ps.partner_id, p.name as partner_name, s.name as service_name,
                      ps.rate_limit_per_minute, ps.status, ps.created_at
                      FROM partner_services ps
                      LEFT JOIN services s ON ps.service_id = s.id
                      LEFT JOIN partners p ON ps.partner_id = p.id
                      $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . "| SQL:" . $query
//                . "| Params:" . json_encode($queryParams)
//                . "| Results==>" . json_encode($results)
//            );

            if (!$results) {
                return $this->HttpResponse(
                    __LINE__ . ":" . __CLASS__
                    . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200,
                    'Request is not successful',
                    [
                        'code' => 404,
                        'message' => 'No partner services found.'
                    ],
                    true
                );
            }

            // Mask sensitive data
            foreach ($results as &$result) {
                if (isset($result['api_key'])) {
                    $result['api_key'] = $this->maskApiKey($result['api_key']);
                }
            }

            // Cache results for future requests (only if not export and not skip_cache)
//            if ($skipCache != 1 && $pagination['export'] != 1) {
//                $cacheData = [
//                    'record_count' => $results[0]['service_count'],
//                    'result' => $results
//                ];
//                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(15)); // 15 minutes cache
//            }

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['service_count'] . ' partner services successfully!',
                    'data' => [
                        'record_count' => $results[0]['service_count'],
                        'result' => $results
                    ]
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(
                __LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is not successful',
                [
                    'code' => 500,
                    'message' => "Internal Server Error."
                ],
                true
            );
        }
    }

    /**
     * CreatePartnerService - Create a new partner service
     * @return type
     */
    function CreatePartnerService()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Service";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request CreatePartnerService :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_id', 'service_id', 'rate_limit_per_minute', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Validate input data
            $validation = ControllerHelpers::validatePartnerServiceData($extractedData);
            if (!$validation['valid']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'Validation failed', 'errors' => $validation['errors']], true);
            }

            $partnerId = $extractedData['partner_id'];
            $serviceId = $extractedData['service_id'];
            $rateLimitPerMinute = $extractedData['rate_limit_per_minute'] ?: 60;
            $status = $extractedData['status'] ?: 'active';

            // Check if partner exists
            $existingPartner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id",
                [':id' => $partnerId]);

            if (!$existingPartner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found!'], true);
            }

            // Check if service exists
            $existingService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM services WHERE id = :id",
                [':id' => $serviceId]);

            if (!$existingService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Service not found!'], true);
            }

            // Check if partner service already exists
            $existingPartnerService = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_services WHERE partner_id = :partner_id AND service_id = :service_id",
                [':partner_id' => $partnerId, ':service_id' => $serviceId]);

            if ($existingPartnerService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner service already exists!'], true);
            }

            // Insert new partner service
            $partnerServiceId = $this->rawInsertBulk('dbUser', 'partner_services', [
                'partner_id' => $partnerId,
                'service_id' => $serviceId,
                'rate_limit_per_minute' => $rateLimitPerMinute,
                'status' => $status,
                'created_at' => $this->now()
            ]);

            if (!$partnerServiceId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner service!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerServicesCache($partnerId);
            RedisUtils::invalidatePartnerCache($partnerId);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResult['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner service created successfully!',
                    'data' => ['partner_service_id' => $partnerServiceId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerService - Update an existing partner service
     * @param int $partnerServiceId
     * @return type
     */
    function UpdatePartnerService($partnerServiceId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Service";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerService :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'rate_limit_per_minute', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            $rateLimitPerMinute = $extractedData['rate_limit_per_minute'];
            $status = $extractedData['status'];

            // Check if partner service exists
            $existingPartnerService = $this->rawSelectOneRecord('dbUser',
                "SELECT id, partner_id FROM partner_services WHERE id = :id",
                [':id' => $partnerServiceId]);

            if (!$existingPartnerService) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner service not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerServiceId];

            if ($rateLimitPerMinute !== null) {
                if (!is_numeric($rateLimitPerMinute)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Rate limit per minute must be numeric!'], true);
                }
                $updateFields[] = "rate_limit_per_minute = :rate_limit_per_minute";
                $updateParams[':rate_limit_per_minute'] = $rateLimitPerMinute;
            }

            if ($status !== null) {
                if (!in_array($status, ['active', 'inactive'])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Status must be either active or inactive!'], true);
                }
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateFields[] = "updated_at = :updated_at";
            $updateParams[':updated_at'] = $this->now();

            $updateSql = "UPDATE partner_services SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner service!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerServicesCache($existingPartnerService['partner_id'], $partnerServiceId);
            RedisUtils::invalidatePartnerCache($existingPartnerService['partner_id']);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner service updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     *
     *  Partner Settings
     *
     **/


    /**
     * GetPartnerSettings
     * @return type
     */
    public function GetPartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Settings";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerSettings :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_ids', 'status', 'skip_cache', 'page', 'limit', 'export'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build pagination parameters
            $pagination = ControllerHelpers::buildPaginationParams($data, $this->settings['SelectRecordLimit']);

            // Validate skip cache parameter
            $skipCache = ControllerHelpers::validateSkipCache($extractedData['skip_cache']);

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_settings', [
                'partner_ids' => $extractedData['partner_ids'],
                'status' => $extractedData['status'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner settings successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            // Build sorting query
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'p.name', 'DESC');
//            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'ps.id', 'DESC');

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            // use find in set
            if (!empty($extractedData['partner_ids']) && $extractedData['partner_ids'] !== 'all') {
                $partnerIds = array_map('trim', explode(',', $extractedData['partner_ids']));
                $partnerIds = array_filter($partnerIds, 'is_numeric'); // ensure only numbers

                if (!empty($partnerIds)) {
                    $findInSetConditions = [];
                    foreach ($partnerIds as $index => $id) {
                        $paramKey = ":partner_id_$index";
                        $findInSetConditions[] = "FIND_IN_SET($paramKey, ps.partner_id)";
                        $searchParams[$paramKey] = (int)$id;
                    }
                    $searchQuery .= " AND (" . implode(' OR ', $findInSetConditions) . ")";
                }
            }

            if (is_numeric($extractedData['status'])) {
                $searchParams[':status'] = $extractedData['status'];
                $searchQuery .= " AND ps.status = :status";
            }

            // Execute query
            $query = "SELECT (SELECT COUNT(ps.id) FROM partner_settings ps $searchQuery) as trx_count,
                    ps.id as setting_id, ps.name, ps.partner_id, p.name as partner_name, ps.api_key, ps.ip_address, ps.callback_url,
                    ps.currency, ps.denomination, ps.timezone, ps.billing_mode, ps.rate_limit, ps.websites, ps.version,
                    ps.updated_by, ps.created_at, ps.updated_at
                    FROM partner_settings ps
                    LEFT JOIN partners p ON ps.partner_id = p.id
                    $searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner settings found.'], true);
            }

            // Mask sensitive data
            foreach ($results as &$result) {
                if (isset($result['api_key'])) {
                    // decrypt api_key
                    $result['api_key'] = $this->Decrypt($result['api_key']);
//                    $result['api_key'] = $this->maskApiKey($result['api_key']);
                }
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(15)); // 15 minutes cache
            }

            return $this->HttpResponse(
                __LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200,
                'Request is successful',
                [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' partner settings successfully!',
                    'data' =>
                        [
                            'record_count' => $results[0]['trx_count'],
                            'result' => $results
                        ]
                ],
                false,
                true
            );

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * CreatePartnerSettings
     * @return type
     */
    public function CreatePartnerSettings()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Create Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request CreatePartnerSettings :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = [
                'timestamp', 'name', 'partner_id', 'api_key', 'ip_address', 'callback_url', 'currency',
                'denomination', 'timezone', 'billing_mode', 'rate_limit', 'websites', 'version'
            ];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $params['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Validate partner exists
            $partner = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partners WHERE id = :id AND status = 1", [':id' => $params['partner_id']]);

            if (!$partner) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner not found or inactive!'], true);
            }

            // Check if partner settings already exist
            $existingSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE partner_id = :partner_id",
                [':partner_id' => $params['partner_id']]);

            if ($existingSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'Partner settings already exist!'], true);
            }

            // Check if API key is unique
            $existingApiKey = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE api_key = :api_key",
                [':api_key' => $params['api_key']]);

            if ($existingApiKey) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 409, 'message' => 'API key already exists!'], true);
            }

            // Insert new partner settings
            $partnerSettingsId = $this->rawInsertBulk('dbUser', 'partner_settings', [
                'partner_id' => $params['partner_id'],
                'name' => $params['name'],
                'api_key' => $this->Encrypt($params['api_key']),
                'ip_address' => $params['ip_address'],
                'callback_url' => $params['callback_url'],
                'currency' => $params['currency'],
                'denomination' => $params['denomination'],
                'timezone' => $params['timezone'],
                'billing_mode' => $params['billing_mode'],
                'rate_limit' => $params['rate_limit'],
                'websites' => $params['websites'] ? json_encode($params['websites']) : null,
                'version' => $params['version'],
                'updated_by' => $authResult['user_id'],
                'created_at' => $this->now(),
                'updated_at' => $this->now()
            ]);

            if (!$partnerSettingsId) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to create partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 201, 'message' => 'Partner settings created successfully!',
                    'data' => ['partner_settings_id' => $partnerSettingsId]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerSettings
     * @return type
     */
    public function UpdatePartnerSettings($partnerSettingsId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Settings";

        $data = $this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerSettings :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = [
                'timestamp', 'name', 'partner_id', 'api_key', 'ip_address', 'callback_url', 'currency',
                'denomination', 'timezone', 'billing_mode', 'rate_limit', 'websites', 'version'
            ];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $params['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Validate partner settings exist
            $partnerSettings = $this->rawSelectOneRecord('dbUser',
                "SELECT id FROM partner_settings WHERE id = :id", [':id' => $partnerSettingsId]);

            if (!$partnerSettings) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner settings not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':id' => $partnerSettingsId];

            if ($params['name'] !== null) {
                $updateFields[] = "name = :name";
                $updateParams[':name'] = $params['name'];
            }
            if ($params['api_key'] !== null) {
                $updateFields[] = "api_key = :api_key";
                $updateParams[':api_key'] = $this->Encrypt($params['api_key']);
            }
            if ($params['ip_address'] !== null) {
                $updateFields[] = "ip_address = :ip_address";
                $updateParams[':ip_address'] = $params['ip_address'];
            }
            if ($params['callback_url'] !== null) {
                $updateFields[] = "callback_url = :callback_url";
                $updateParams[':callback_url'] = $params['callback_url'];
            }
            if ($params['currency'] !== null) {
                $updateFields[] = "currency = :currency";
                $updateParams[':currency'] = $params['currency'];
            }
            if ($params['denomination'] !== null) {
                $updateFields[] = "denomination = :denomination";
                $updateParams[':denomination'] = $params['denomination'];
            }
            if ($params['timezone'] !== null) {
                $updateFields[] = "timezone = :timezone";
                $updateParams[':timezone'] = $params['timezone'];
            }
            if ($params['billing_mode'] !== null) {
                $updateFields[] = "billing_mode = :billing_mode";
                $updateParams[':billing_mode'] = $params['billing_mode'];
            }
            if ($params['rate_limit'] !== null) {
                $updateFields[] = "rate_limit = :rate_limit";
                $updateParams[':rate_limit'] = $params['rate_limit'];
            }
            if ($params['websites'] !== null) {
                $updateFields[] = "websites = :websites";
                $updateParams[':websites'] = json_encode($params['websites']);
            }
            if ($params['version'] !== null) {
                $updateFields[] = "version = :version";
                $updateParams[':version'] = $params['version'];
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateSql = "UPDATE partner_settings SET " . implode(', ', $updateFields) . " WHERE id = :id";
            $result = $this->rawUpdateWithParams('dbUser', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__,
                    200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner settings!'], true);
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner settings updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__
                . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString()
                . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful',
                ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


    /**
     * Mask API key for security
     * @param string $apiKey
     * @return string
     */
    private function maskApiKey($apiKey)
    {
        if (strlen($apiKey) <= 8) {
            return str_repeat('*', strlen($apiKey));
        }
        return substr($apiKey, 0, 4) . str_repeat('*', strlen($apiKey) - 8) . substr($apiKey, -4);
    }


    /**
     *
     * Bets
     *
     **/

    /**
     * GetPartnerBets
     * @return type
     */
    function GetPartnerBets()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bets";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerBets :" . json_encode($data));

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_ids', 'bet_currency', 'bet_type', 'status', 'start', 'end', 'skip_cache'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }
            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . " | Authenticated User: " . json_encode($authResult)
                . " | Authenticated User: " . json_encode($authResult['user_data'])
            );

            // Build pagination and sorting
            $pagination = ControllerHelpers::buildPaginationParams($data);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pb.bet_id', 'DESC');

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_bets', [
                'partner_ids' => $extractedData['partner_ids'],
                'bet_currency' => $extractedData['bet_currency'],
                'bet_type' => $extractedData['bet_type'],
                'status' => $extractedData['status'],
                'start' => $extractedData['start'],
                'end' => $extractedData['end'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            $skipCache = $extractedData['skip_cache'] ?: 0;
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner bets successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            // Build search query
            $searchQuery = "WHERE 1=1";
            $searchParams = [];

            // use find in set
            if (!empty($extractedData['partner_ids']) && $extractedData['partner_ids'] !== 'all') {
                $partnerIds = array_map('trim', explode(',', $extractedData['partner_ids']));
                $partnerIds = array_filter($partnerIds, 'is_numeric'); // ensure only numbers

                if (!empty($partnerIds)) {
                    $findInSetConditions = [];
                    foreach ($partnerIds as $index => $id) {
                        $paramKey = ":partner_id_$index";
                        $findInSetConditions[] = "FIND_IN_SET($paramKey, pb.partner_id)";
                        $searchParams[$paramKey] = (int)$id;
                    }
                    $searchQuery .= " AND (" . implode(' OR ', $findInSetConditions) . ")";
                }
            }

            if ($extractedData['bet_currency']) {
                $searchParams[':bet_currency'] = $extractedData['bet_currency'];
                $searchQuery .= " AND pb.bet_currency = :bet_currency";
            }

            if ($extractedData['bet_type']) {
                $searchParams[':bet_type'] = $extractedData['bet_type'];
                if ($extractedData['bet_type'] == 1) {
                    $searchQuery .= " AND pb.total_games = 1";
                } else {
                    $searchQuery .= " AND pb.total_games > 1";
                }
            }

            if ($extractedData['status']) {
                $searchParams[':status'] = $extractedData['status'];
                $searchQuery .= " AND pb.status = :status";
            }

            if ($extractedData['start'] && $extractedData['end']) {
                $searchParams[':start'] = $extractedData['start'] . " 00:00:00";
                $searchParams[':end'] = $extractedData['end'] . " 23:59:59";
                $searchQuery .= " AND pb.created_at BETWEEN :start AND :end";
            } else if ($extractedData['start']) {
                $searchParams[':start'] = $extractedData['start'];
                $searchQuery .= " AND DATE(pb.created_at) >= :start";
            } else if ($extractedData['end']) {
                $searchParams[':end'] = $extractedData['end'];
                $searchQuery .= " AND DATE(pb.created_at) <= :end";
            }

            if ($pagination['export'] == 1) {
                // For export, remove pagination and apply export-specific limits
                $orderBy = $searchParams['sort'] ? "ORDER BY " . $searchParams['sort'] . " " . $searchParams['order'] : "ORDER BY pb.bet_id DESC";
                $exportLimit = 100000; // Large limit for exports
                $sorting = "$orderBy LIMIT $exportLimit";
            }

            // Execute query with betting-specific context
            $query = "SELECT (SELECT COUNT(pb.bet_id) FROM partners_bets pb $searchQuery) as trx_count, "
                . "pb.bet_id, pb.partner_id, p.name as partner_name, pb.profile_id, pb.bet_currency, "
                . "pr.msisdn, pr.name as profile_name, pr.ip_address, "
                . "pb.bet_amount, pb.bet_reference, pb.bet_transaction_id, pb.bet_credit_transaction_id, "
                . " pb.bet_type, pb.total_games, pb.live_events, pb.total_odd, pb.possible_win, "
                . " pb.witholding_tax, pb.excise_tax, pb.bet_attribution, pb.browser_details, "
                . "pb.extra_data, pb.created_by, pb.kra_report, pb.risk_state, pb.processed, "
                . "pb.status, pb.created_at, pb.updated_at, "
                . "CASE "
                . "WHEN pb.status = 0 THEN 'Pending' "
                . "WHEN pb.status = 1 THEN 'Won' "
                . "WHEN pb.status = 2 THEN 'Lost' "
                . "WHEN pb.status = 3 THEN 'Cancelled' "
                . "ELSE 'Unknown' "
                . "END as status_description "
                . "FROM partners_bets pb "
                . "LEFT JOIN mossbets_profile.profile pr ON pb.profile_id = pr.id "
                . "LEFT JOIN partners p ON pb.partner_id = p.id "
                . "$searchQuery $sorting";

            $results = $this->rawSelect('dbBetsRead', $query, $searchParams);
            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . "| Getting Partner Bets:"
                . "| SQL:" . $query
                . "| Params:" . json_encode($searchParams)
//                . "| Results==>" . json_encode($results)
            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bets found.'], true);
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(10)); // 10 minutes cache
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Queried ' . $results[0]['trx_count'] . ' partner bets successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * GetPartnersBetSlips
     * @return type
     */
    function GetPartnersBetSlips()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Bet Slips";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        try {
            // Extract authentication headers using helper
            $headers = array_change_key_case($this->request->getHeaders(), CASE_LOWER);
            $authData = ControllerHelpers::extractAuthHeaders($headers);

            // Extract request data using standardized pattern
            $requestFields = ['timestamp', 'partner_ids', 'bet_id', 'sport_id', 'status', 'live_bet', 'skip_cache'];
            $params = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $params['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                $errorData = $errorResponse['error'];
                if ($errorResponse['technical_details']) {
                    $errorData['technical_message'] = $errorResponse['technical_details'];
                }
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorData, true);
            }

            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pbs.slip_id', 'DESC');
            $searchParams = ControllerHelpers::extractSearchParams($data);
            $dateRange = ControllerHelpers::extractDateRange($data);

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_bet_slips', [
                'partner_ids' => $params['partner_ids'],
                'bet_id' => $params['bet_id'],
                'sport_id' => $params['sport_id'],
                'status' => $params['status'],
                'live_bet' => $params['live_bet'],
                'start_date' => $dateRange['start_date'],
                'end_date' => $dateRange['end_date'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            $skipCache = $params['skip_cache'] ?: 0;
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner bet slips successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            // Build search query
            $searchQuery = "WHERE 1=1";
            $queryParams = [];


            // use find in set
            if (!empty($params['partner_ids']) && $params['partner_ids'] !== 'all') {
                $partnerIds = array_map('trim', explode(',', $params['partner_ids']));
                $partnerIds = array_filter($partnerIds, 'is_numeric'); // ensure only numbers

                if (!empty($partnerIds)) {
                    $findInSetConditions = [];
                    foreach ($partnerIds as $index => $id) {
                        $paramKey = ":partner_id_$index";
                        $findInSetConditions[] = "FIND_IN_SET($paramKey, pbs.partner_id)";
                        $queryParams[$paramKey] = (int)$id;
                    }
                    $searchQuery .= " AND (" . implode(' OR ', $findInSetConditions) . ")";
                }
            }

            if ($params['bet_id']) {
                $searchQuery .= " AND pbs.bet_id = :bet_id";
                $queryParams[':bet_id'] = $params['bet_id'];
            }

            if ($params['sport_id']) {
                $searchQuery .= " AND pbs.sport_id = :sport_id";
                $queryParams[':sport_id'] = $params['sport_id'];
            }

            if ($params['status'] !== false) {
                $searchQuery .= " AND pbs.status = :status";
                $queryParams[':status'] = $params['status'];
            }

            if ($params['live_bet'] !== false) {
                $searchQuery .= " AND pbs.live_bet = :live_bet";
                $queryParams[':live_bet'] = $params['live_bet'];
            }

            if ($dateRange['start_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) >= :start_date";
                $queryParams[':start_date'] = $dateRange['start_date'];
            }

            if ($dateRange['end_date']) {
                $searchQuery .= " AND DATE(pbs.created_at) <= :end_date";
                $queryParams[':end_date'] = $dateRange['end_date'];
            }

            // Build sorting
//            $sorting = $this->tableQueryBuilder($searchParams['sort'], $searchParams['order'],
//                $pagination['page'], $pagination['limit']);
//
//            if ($pagination['export'] == 1) {
//                // For export, remove pagination and apply export-specific limits
//                $orderBy = $searchParams['sort'] ? "ORDER BY " . $searchParams['sort'] . " " . $searchParams['order'] : "ORDER BY pbs.slip_id DESC";
//                $exportLimit = 100000; // Large limit for exports
//                $sorting = "$orderBy LIMIT $exportLimit";
//            }

            // Execute query
            $query = "SELECT (SELECT COUNT(pbs.slip_id) FROM partners_bet_slips pbs $searchQuery) as trx_count, "
                . "pbs.slip_id, p.name as partner_name, pbs.bet_id, pbs.sport_id, s.sport_name, mk.market_name, "
                . "pbs.parent_match_id, f.competitor1 as home_team, f.competitor2 as away_team, pbs.parent_market_id, "
                . "pbs.selection_id, pbs.outcome_name, pbs.odd_value, pbs.pick, pbs.pick_name, pbs.winning_outcome, "
                . "pbs.market_id, pbs.ht_scores, pbs.ft_scores, pbs.et_scores, pbs.extra_data, pbs.live_bet, "
                . "pbs.status, pbs.resulting_type, pbs.start_time, pbs.created_at, pbs.updated_at "
                . "FROM partners_bet_slips pbs "
                . "LEFT JOIN mossbets_sports_book.fixture f ON pbs.parent_match_id = f.match_id "
                . "LEFT JOIN mossbets_sports_book.markets mk ON pbs.parent_market_id = mk.market_id "
                . "LEFT JOIN mossbets_sports_book.sports s ON pbs.sport_id = s.sport_id "
                . "LEFT JOIN partners p ON pbs.partner_id = p.id "
                . "$searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $query, $queryParams);

//            $this->infologger->info(__LINE__ . ":" . __CLASS__
//                . "BetsSlip Api " . $query
//                . "BetsSlip Api " . json_encode($results)
//            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner bet slips found!'], true);
            }

            // Format numeric values
            foreach ($results as &$result) {
                $result['odd_value'] = number_format((float)$result['odd_value'], 2, '.', '');
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(10)); // 10 minutes cache
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' Partner Bet Slips successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * UpdatePartnerBet - Update an existing partner bet
     * @param int $betId
     * @return type
     */
    function UpdatePartnerBet($betId)
    {
        $start_time = $this->getMicrotime();
        $permissionName = "Update Partner Bet";

        $data = (array)$this->request->getJsonRawBody();
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request UpdatePartnerBet :" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'status', 'risk_state', 'processed', 'kra_report'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Authenticate user
            $authResponse = Authenticate::AuthenticateUser($authData);
            if (!$authResponse['status']) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 401, 'message' => $authResponse['message']], true);
            }

            // Check user permissions
            if (!UserUtils::CheckUserPermission($authResponse['data']['user_id'], $permissionName)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 403, 'message' => 'Access denied!'], true);
            }

            $status = $extractedData['status'];
            $riskState = $extractedData['risk_state'];
            $processed = $extractedData['processed'];
            $kraReport = $extractedData['kra_report'];

            // Check if bet exists
            $existingBet = $this->rawSelectOneRecord('dbUser',
                "SELECT bet_id, partner_id FROM partners_bets WHERE bet_id = :bet_id",
                [':bet_id' => $betId]);

            if (!$existingBet) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 404, 'message' => 'Partner bet not found!'], true);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateParams = [':bet_id' => $betId];

            if ($status !== null) {
                if (!in_array($status, [0, 1, 2, 3])) { // 0=Pending, 1=Won, 2=Lost, 3=Cancelled
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Invalid status value!'], true);
                }
                $updateFields[] = "status = :status";
                $updateParams[':status'] = $status;
            }

            if ($riskState !== null) {
                if (!is_numeric($riskState)) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Risk state must be numeric!'], true);
                }
                $updateFields[] = "risk_state = :risk_state";
                $updateParams[':risk_state'] = $riskState;
            }

            if ($processed !== null) {
                if (!in_array($processed, [0, 1])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'Processed must be 0 or 1!'], true);
                }
                $updateFields[] = "processed = :processed";
                $updateParams[':processed'] = $processed;
            }

            if ($kraReport !== null) {
                if (!in_array($kraReport, [0, 1])) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                        ['code' => 422, 'message' => 'KRA report must be 0 or 1!'], true);
                }
                $updateFields[] = "kra_report = :kra_report";
                $updateParams[':kra_report'] = $kraReport;
            }

            if (empty($updateFields)) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 422, 'message' => 'No fields to update!'], true);
            }

            $updateFields[] = "updated_at = :updated_at";
            $updateParams[':updated_at'] = $this->now();

            $updateSql = "UPDATE partners_bets SET " . implode(', ', $updateFields) . " WHERE bet_id = :bet_id";
            $result = $this->rawUpdateWithParams('dbBetsWrite', $updateSql, $updateParams);

            if (!$result) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__, 200, 'Request is not successful',
                    ['code' => 500, 'message' => 'Failed to update partner bet!'], true);
            }

            // Invalidate related caches
            RedisUtils::invalidatePartnerBetsCache($existingBet['partner_id'], $betId);
            RedisUtils::invalidatePartnerCache($existingBet['partner_id']);

            // Log user activity
            UserUtils::LogUserActivity([
                'user_id' => $authResponse['data']['user_id'],
                'activity' => strtoupper(__CLASS__) . " => " . strtoupper(__FUNCTION__),
                'request' => $data,
                'created_at' => $this->now(),
            ]);

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__
                . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful',
                ['code' => 200, 'message' => 'Partner bet updated successfully!'], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }



    /**
     *
     *  Partner Balances
     *
     **/

    /**
     * GetPartnerBalances
     * @param int $partnetId
     * @return type
     */
    function GetPartnersBalances()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Balances";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress . "] | Request GetPartnerBalances :" . json_encode($data));
        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_ids', 'balance_min', 'balance_max', 'bonus_min', 'bonus_max', 'skip_cache', 'page', 'limit', 'export', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }


            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);
            $skipCache = ControllerHelpers::validateSkipCache($extractedData['skip_cache']);

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_balances', [
                'partner_ids' => $extractedData['partner_ids'],
                'balance_min' => $extractedData['balance_min'],
                'balance_max' => $extractedData['balance_max'],
                'bonus_min' => $extractedData['bonus_min'],
                'bonus_max' => $extractedData['bonus_max'],
                'status' => $extractedData['status'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner users successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            // Handle partner_ids (can be "2" or "3,8,30") :use find in set
            if (!empty($extractedData['partner_ids']) && $extractedData['partner_ids'] !== 'all') {
                $partnerIds = array_map('trim', explode(',', $extractedData['partner_ids']));
                $partnerIds = array_filter($partnerIds, 'is_numeric'); // ensure only numbers

                if (!empty($partnerIds)) {
                    $findInSetConditions = [];
                    foreach ($partnerIds as $index => $id) {
                        $paramKey = ":partner_id_$index";
                        $findInSetConditions[] = "FIND_IN_SET($paramKey, pb.partner_id)";
                        $searchParams[$paramKey] = (int)$id;
                    }
                    $searchQuery .= " AND (" . implode(' OR ', $findInSetConditions) . ")";
                }
            }


            // Balance filters
            if (!empty($extractedData['balance_min']) && !empty($extractedData['balance_max'])) {
                $searchQuery .= " AND pb.balance BETWEEN :balance_min AND :balance_max";
                $searchParams[':balance_min'] = $extractedData['balance_min'];
                $searchParams[':balance_max'] = $extractedData['balance_max'];
            } elseif (!empty($extractedData['balance_min'])) {
                $searchQuery .= " AND pb.balance >= :balance_min";
                $searchParams[':balance_min'] = $extractedData['balance_min'];
            } elseif (!empty($extractedData['balance_max'])) {
                $searchQuery .= " AND pb.balance <= :balance_max";
                $searchParams[':balance_max'] = $extractedData['balance_max'];
            }

            // Bonus filters
            if (!empty($extractedData['bonus_min']) && !empty($extractedData['bonus_max'])) {
                $searchQuery .= " AND pb.bonus BETWEEN :bonus_min AND :bonus_max";
                $searchParams[':bonus_min'] = $extractedData['bonus_min'];
                $searchParams[':bonus_max'] = $extractedData['bonus_max'];
            } elseif (!empty($extractedData['bonus_min'])) {
                $searchQuery .= " AND pb.bonus >= :bonus_min";
                $searchParams[':bonus_min'] = $extractedData['bonus_min'];
            } elseif (!empty($extractedData['bonus_max'])) {
                $searchQuery .= " AND pb.bonus <= :bonus_max";
                $searchParams[':bonus_max'] = $extractedData['bonus_max'];
            }

            // Status
            if (isset($extractedData['status']) && is_numeric($extractedData['status'])) {
                $searchQuery .= " AND u.status = :status";
                $searchParams[':status'] = $extractedData['status'];
            }

            // Build sorting query
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'pb.id', 'DESC');

            // Final query
            $sql = "SELECT (SELECT COUNT(p.id) FROM partners p $searchQuery) as trx_count, "
                . "p.id, p.name, p.status, p.email_address, p.dial_code, p.msisdn, p.address, p.country, "
                . "SUM(pb.balance) as balance, SUM(pb.bonus) as bonus, pb.updated_at as last_updated "
                . "FROM partners p "
                . "LEFT JOIN partner_balance pb ON p.id = pb.partner_id "
                . "$searchQuery "
                . "GROUP BY p.id "
                . "$sorting";


            $results = $this->rawSelect('dbUser', $sql, $searchParams);

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner balances found!'], true);
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(15)); // 15 minutes cache
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' partner balances successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }



    /**
     *
     *  Partner Users
     *
     **/

    /**
     * GetPartnerUsers
     * @param int $partnetId
     * @return type
     */
    function GetPartnerUsers()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Partner Users";

        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }
        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . "[" . $this->ipAddress
            . "] | Request GetPartnerUsers :" . json_encode($data)
        );
        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper
            $requestFields = ['timestamp', 'partner_ids', 'name', 'skip_cache', 'page', 'limit', 'export', 'status'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }


            // Extract pagination and search parameters
            $pagination = ControllerHelpers::extractPaginationParams($data, $this->settings['SelectRecordLimit']);
            $searchParams = ControllerHelpers::extractSearchParams($data);
            $skipCache = ControllerHelpers::validateSkipCache($extractedData['skip_cache']);

            // Generate cache key
            $cacheKey = RedisUtils::generateCacheKey('partner_users', [
                'partner_ids' => $extractedData['partner_ids'],
                'status' => $extractedData['status'],
                'page' => $pagination['page'],
                'limit' => $pagination['limit']
            ]);

            // Check cache first (unless skip_cache is set)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cachedResults = RedisUtils::redisRawSelectData($cacheKey);
                if ($cachedResults) {
                    return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                        200, 'Request is successful',
                        ['code' => 200, 'message' => 'Queried ' . $cachedResults->record_count . ' partner users successfully! (cached)',
                            'data' => ['record_count' => $cachedResults->record_count, 'result' => $cachedResults->result]], false, true);
                }
            }

            $searchQuery = " WHERE 1";
            $searchParams = [];

            // update partner_ids to partner_ids (array)
            if (!empty($extractedData['partner_ids']) && $extractedData['partner_ids'] !== 'all') {
                $partnerIds = array_map('trim', explode(',', $extractedData['partner_ids']));
                $partnerIds = array_filter($partnerIds, 'is_numeric'); // ensure only numbers

                if (!empty($partnerIds)) {
                    $findInSetConditions = [];
                    foreach ($partnerIds as $index => $id) {
                        $paramKey = ":partner_id_$index";
                        $findInSetConditions[] = "FIND_IN_SET($paramKey, u.partner_ids)";
                        $searchParams[$paramKey] = (int)$id;
                    }
                    $searchQuery .= " AND (" . implode(' OR ', $findInSetConditions) . ")";
                }
            }

            if (is_numeric($extractedData['status'])) {
                $searchQuery .= " AND u.status = :status";
                $searchParams[':status'] = $extractedData['status'];
            }

            // Build sorting query
            $sorting = ControllerHelpers::buildSortingQuery($pagination, 'u.id', 'DESC');

            // Query
            $sql = "SELECT (SELECT COUNT(u.id) FROM user u $searchQuery) as trx_count, "
                . "u.id, u.partner_ids, u.display_name, u.user_name, u.msisdn, u.status, u.type, ul.role_id, "
                . "ur.name as role_name, ul.status as login_status, ul.last_logged_on, ul.activation_date, "
                . "u.created_at, u.updated_at "
                . "FROM user u "
                . "LEFT JOIN user_login ul ON u.id = ul.user_id "
                . "LEFT JOIN user_roles ur ON ul.role_id = ur.id "
                . "$searchQuery $sorting";

            $results = $this->rawSelect('dbUser', $sql, $searchParams);

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . "| SQL??????:" . $sql
                . "| Params:" . json_encode($searchParams)
                . "| Results==>" . json_encode($results)
            );

            if (!$results) {
                return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                    200, 'Request is not successful', ['code' => 404, 'message' => 'No partner users found!'], true);
            }

            // Cache results for future requests (only if not export and not skip_cache)
            if ($skipCache != 1 && $pagination['export'] != 1) {
                $cacheData = [
                    'record_count' => $results[0]['trx_count'],
                    'result' => $results
                ];
                RedisUtils::redisRawInsertData($cacheKey, $cacheData, RedisUtils::SetTimeoutInSeconds(15)); // 15 minutes cache
            }

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Queried ' . $results[0]['trx_count'] . ' partner users successfully!',
                    'data' => ['record_count' => $results[0]['trx_count'], 'result' => $results]
                ], false, true);
        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }


}