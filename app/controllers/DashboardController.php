<?php

require_once 'app/utils/ControllerHelpers.php';

/**
 * Description of DashboardController
 *
 * <AUTHOR>
 */
class DashboardController extends \ControllerBase
{

    /**
     * GetStats - Get comprehensive dashboard statistics with partner filtering
     * @return type
     */
    function GetStats()
    {
        $start_time = $this->getMicrotime();
        $permissionName = "View Dashboard Statistics";

        // Extract request data using ControllerHelper pattern
        $data = $this->request->get();
        if ($this->request->getMethod() == 'GET') {
            unset($data['_url']);
        } else {
            $data = (array)$this->request->getJsonRawBody();
        }

        $this->infologger->info(__LINE__ . ":" . __CLASS__
            . " | Request GetStats:" . json_encode($data));

        try {
            // Extract authentication headers using ControllerHelper
            $authData = ControllerHelpers::extractAuthHeaders($this->request->getHeaders());

            // Extract request data using ControllerHelper - including partner_ids and date filtering
            $requestFields = ['timestamp', 'partner_ids', 'period', 'start_date','end_date', 'start','end', 'export', 'chart_type', 'skip_cache'];
            $extractedData = ControllerHelpers::extractRequestData($data, $requestFields);
            $authData['timestamp'] = $extractedData['timestamp'];

            $period = $extractedData['period'] ?: '7days';
            $export = $extractedData['export'] ?: false;
            $chartType = $extractedData['chart_type'] ?: 'all';
            $startDate = $extractedData['start'] ?: $extractedData['start_date'];
            $endDate = $extractedData['end'] ?: $extractedData['end_date'];

            // Complete authentication and authorization flow
            $authResult = ControllerHelpers::authenticateAndAuthorize($authData, $permissionName, ['timestamp']);
            if (!$authResult['success']) {
                $errorResponse = ControllerHelpers::buildAuthFailureResponse($authResult, __CLASS__, __LINE__);
                return $this->HttpResponse($errorResponse['line_class'], $errorResponse['status_code'],
                    'Request is not successful', $errorResponse['error'], true);
            }

            // Build partner filter condition
            $partnerCondition = $this->buildPartnerCondition($extractedData['partner_ids']);

            // Build date filter condition (prioritize start/end over period)
            $dateCondition = $this->buildDateCondition($startDate, $endDate, $period);

            // Debug logging
            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . " | Partner Condition:" . json_encode($partnerCondition)
                . " | Date Condition:" . json_encode($dateCondition));

            $this->infologger->info(__LINE__ . ":" . __CLASS__
                . " | Partner Condition:" . json_encode($partnerCondition)
                . " | Date Condition:" . json_encode($dateCondition)
            );

            // 1. User Statistics (filtered by partner_ids and date range if provided)
            $userQuery = "SELECT
                    COUNT(*) as total_users,
                    SUM(CASE WHEN u.status = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN u.status = 2 THEN 1 ELSE 0 END) as inactive_users,
                    SUM(CASE WHEN u.status = 3 THEN 1 ELSE 0 END) as suspended_users,
                    SUM(CASE WHEN DATE(u.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_users_30days
                 FROM user u WHERE 1=1 " . $partnerCondition['user_condition'] . $dateCondition['user_condition'];

            $userParams = array_merge($partnerCondition['params'], $dateCondition['params']);
            $userStats = $this->rawSelectOneRecord('dbUser', $userQuery, $userParams);

            // 2. Partner Statistics (filtered by partner_ids and date range if provided)
            $partnerQuery = "SELECT
                    COUNT(*) as total_partners,
                    SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as active_partners,
                    SUM(pb.balance) as total_balance,
                    AVG(pb.balance) as average_balance,
                    MAX(pb.balance) as highest_balance,
                    SUM(pb.bonus) as total_bonus,
                    AVG(pb.bonus) as average_bonus,
                    MAX(pb.bonus) as highest_bonus,
                    SUM(CASE WHEN DATE(p.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as new_partners_30days
                 FROM partners p
                 LEFT JOIN partner_balance pb ON p.id = pb.partner_id AND pb.status = 1
                 WHERE 1=1 " . $partnerCondition['partner_condition'] . $dateCondition['partner_condition'];

            $partnerParams = array_merge($partnerCondition['params'], $dateCondition['params']);
            $partnerStats = $this->rawSelectOneRecord('dbUser', $partnerQuery, $partnerParams);

            // 3. Betting Statistics (filtered by partner_ids and date range if provided)
            $bettingQuery = "SELECT
                    COUNT(*) as total_bets,
                    SUM(pb.bet_amount) as total_stakes,
                    SUM(CASE WHEN pb.status = 1 THEN pb.possible_win ELSE 0 END) as total_winnings,
                    (SUM(pb.bet_amount) - SUM(CASE WHEN pb.status = 1 THEN pb.possible_win ELSE 0 END)) as net_revenue,
                    AVG(pb.bet_amount) as average_stake,
                    COUNT(DISTINCT pb.partner_id) as betting_partners
                 FROM partners_bets pb
                 LEFT JOIN partners p ON pb.partner_id = p.id
                 WHERE pb.status IN (0,1,2,3) " . $dateCondition['betting_condition'] . $partnerCondition['partner_condition'];

            $bettingParams = array_merge($partnerCondition['params'], $dateCondition['params']);
            $bettingStats = $this->rawSelectOneRecord('dbUser', $bettingQuery, $bettingParams);

            // 4. System Statistics (not filtered by partner)
            $systemStats = $this->rawSelectOneRecord('dbUser',
                "SELECT
                    COUNT(*) as total_roles,
                    (SELECT COUNT(*) FROM user_permissions) as total_permissions
                 FROM user_roles", []);

            // 5. Generate Chart Data
            $chartData = [];

            // Partner Balance Chart (Pie Chart)
//            if ($chartType === 'all' || $chartType === 'partner_balance') {
            if ($chartType === 'partner_balance') {
                $balanceQuery = "SELECT p.name as partner_name, pb.balance, pb.bonus
                     FROM partner_balance pb
                     JOIN partners p ON pb.partner_id = p.id
                     WHERE pb.status = 1 " . $partnerCondition['partner_condition'] . $dateCondition['balance_condition'] . "
                     ORDER BY pb.balance DESC
                     LIMIT 10";

                $balanceParams = array_merge($partnerCondition['params'], $dateCondition['params']);
                $balanceData = $this->rawSelect('dbUser', $balanceQuery, $balanceParams);

                if ($balanceData) {
                    $chartData['partner_balance'] = [
                        'title' => 'Top 10 Partners by Balance',
                        'type' => 'doughnut',
                        'data' => $balanceData
                    ];
                }
            }


            // Betting Trends Chart (Line Chart)
            if ($chartType === 'all' || $chartType === 'betting_trends') {
                $trendsQuery = "SELECT DATE(created_at) as date,
                        COUNT(*) as bet_count,
                        SUM(pb.bet_amount) as total_stakes,
                        SUM(CASE WHEN pb.status = 1 THEN pb.possible_win ELSE 0 END) as total_winnings
                     FROM partners_bets pb
                     JOIN partners p ON pb.partner_id = p.id
                     WHERE pb.status IN (0,1,2,3) " . $dateCondition['betting_condition'] . $partnerCondition['partner_condition'] . "
                     GROUP BY DATE(pb.created_at)
                     ORDER BY date DESC
                     LIMIT 30";

                $trendsParams = array_merge($partnerCondition['params'], $dateCondition['params']);
//                $trendsData = $this->rawSelect('dbUser', $trendsQuery, $trendsParams);

//                if ($trendsData) {
//                    $chartData['betting_trends'] = [
//                        'title' => 'Betting Trends (' . ($startDate && $endDate ? "$startDate to $endDate" : ucfirst($period)) . ')',
//                        'type' => 'line',
//                        'data' => $trendsData
//                    ];
//                }
            }

            // Partner Performance Chart (Bar Chart)
            if ($chartType === 'all' || $chartType === 'partner_performance') {
                $performanceQuery = "SELECT p.name as partner_name,
                        COUNT(pb.bet_id) as total_bets,
                        SUM(pb.bet_amount) as total_stakes,
                        SUM(CASE WHEN pb.status = 1 THEN pb.possible_win ELSE 0 END) as total_winnings,
                        (SUM(pb.bet_amount) - SUM(CASE WHEN pb.status = 1 THEN pb.possible_win ELSE 0 END)) as net_revenue
                     FROM partners_bets pb
                     JOIN partners p ON pb.partner_id = p.id
                     WHERE pb.status IN (0,1,2,3) " . $dateCondition['betting_condition'] . $partnerCondition['partner_condition'] . "
                     GROUP BY p.id, p.name
                     ORDER BY net_revenue DESC
                     LIMIT 10";

                $performanceParams = array_merge($partnerCondition['params'], $dateCondition['params']);
                $performanceData = $this->rawSelect('dbUser', $performanceQuery, $performanceParams);

                if ($performanceData) {
                    $chartData['partner_performance'] = [
                        'title' => 'Top 10 Partners by Net Revenue (' . ($startDate && $endDate ? "$startDate to $endDate" : ucfirst($period)) . ')',
                        'type' => 'bar',
                        'data' => $performanceData
                    ];
                }
            }

            // Build comprehensive dashboard data
            $dashboardData = [
                'cards' => [
                    'users' => [
                        'total' => (int)($userStats['total_users'] ?? 0),
                        'active' => (int)($userStats['active_users'] ?? 0),
                        'inactive' => (int)($userStats['inactive_users'] ?? 0),
                        'suspended' => (int)($userStats['suspended_users'] ?? 0),
                        'new_this_month' => (int)($userStats['new_users_30days'] ?? 0)
                    ],
                    'partners' => [
                        'total' => (int)($partnerStats['total_partners'] ?? 0),
                        'active' => (int)($partnerStats['active_partners'] ?? 0),
                        'total_balance' => number_format((float)($partnerStats['total_balance'] ?? 0), 2),
                        'average_balance' => number_format((float)($partnerStats['average_balance'] ?? 0), 2),
                        'highest_balance' => number_format((float)($partnerStats['highest_balance'] ?? 0), 2),
                        'total_bonus' => number_format((float)($partnerStats['total_bonus'] ?? 0), 2),
                        'average_bonus' => number_format((float)($partnerStats['average_bonus'] ?? 0), 2),
                        'highest_bonus' => number_format((float)($partnerStats['highest_bonus'] ?? 0), 2),
                        'new_this_month' => (int)($partnerStats['new_partners_30days'] ?? 0)
                    ],
                    'betting' => [
                        'total_bets' => (int)($bettingStats['total_bets'] ?? 0),
                        'total_stakes' => number_format((float)($bettingStats['total_stakes'] ?? 0), 2),
                        'total_winnings' => number_format((float)($bettingStats['total_winnings'] ?? 0), 2),
                        'net_revenue' => number_format((float)($bettingStats['net_revenue'] ?? 0), 2),
                        'average_stake' => number_format((float)($bettingStats['average_stake'] ?? 0), 2),
                        'betting_partners' => (int)($bettingStats['betting_partners'] ?? 0)
                    ],
                    'system' => [
                        'total_roles' => (int)($systemStats['total_roles'] ?? 0),
                        'total_permissions' => (int)($systemStats['total_permissions'] ?? 0),
                        'active_channels' => (int)($systemStats['active_channels'] ?? 0),
                        'period' => $period
                    ]
                ],
                'charts' => $chartData,
                'filters' => [
                    'partner_ids' => $extractedData['partner_ids'],
                    'period' => $period,
                    'chart_type' => $chartType,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'date_filter_applied' => !empty($startDate) && !empty($endDate)
                ]
            ];

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is successful', [
                    'code' => 200,
                    'message' => 'Dashboard statistics retrieved successfully!',
                    'data' => $dashboardData,
                    'summary' => [
                        'total_charts' => count($chartData),
                        'period' => $period,
                        'chart_type' => $chartType,
                        'partner_filter' => !empty($extractedData['partner_ids']) ? 'Applied' : 'None'
                    ]
                ], false, true);

        } catch (Exception $ex) {
            $this->errorlogger->emergency(__LINE__ . ":" . __CLASS__ . "| TAT " . $this->CalculateTAT($start_time) . " Sec(s)"
                . "| Exception Trace:" . $ex->getTraceAsString() . "| Message:" . $ex->getMessage());

            return $this->HttpResponse(__LINE__ . ":" . __CLASS__ . "| Took " . $this->CalculateTAT($start_time) . " Sec",
                200, 'Request is not successful', ['code' => 500, 'message' => "Internal Server Error."], true);
        }
    }

    /**
     * Helper method to build partner filtering conditions
     * @param string $partnerIds
     * @return array
     */
    private function buildPartnerCondition($partnerIds)
    {
        $conditions = [
            'partner_condition' => '',
            'user_condition' => '',
            'params' => []
        ];

        if (!empty($partnerIds) && $partnerIds !== 'all') {
            $partner_ids = array_map('trim', explode(',', $partnerIds));
            // Keep only numeric values to avoid SQL injection
            $partner_ids = array_filter($partner_ids, 'is_numeric');

            if (!empty($partner_ids)) {
                // Use the same parameter names for both conditions to avoid conflicts
                $placeholders = [];
                foreach ($partner_ids as $index => $id) {
                    $paramKey = ":partner_id_$index";
                    $placeholders[] = $paramKey;
                    $conditions['params'][$paramKey] = (int)$id;
                }

                // For direct partner filtering
                $conditions['partner_condition'] = " AND p.id IN (" . implode(',', $placeholders) . ")";

                // For user filtering by partner_ids (comma-separated string field)
                // Use FIND_IN_SET for comma-separated values like "2,3" or "2"
                $userConditions = [];
                foreach ($partner_ids as $index => $id) {
                    $paramKey = ":partner_id_$index"; // Use same parameter name
                    $userConditions[] = "FIND_IN_SET($paramKey, u.partner_ids) > 0";
                    // Parameter already added above, no need to add again
                }
                $conditions['user_condition'] = " AND (" . implode(' OR ', $userConditions) . ")";
            }
        }

        return $conditions;
    }

    /**
     * Helper method to build date filtering conditions
     * @param string $startDate
     * @param string $endDate
     * @param string $period
     * @return array
     */
    private function buildDateCondition($startDate, $endDate, $period)
    {
        $conditions = [
            'user_condition' => '',
            'partner_condition' => '',
            'betting_condition' => '',
            'balance_condition' => '',
            'params' => []
        ];

        // If both start and end dates are provided, use them
        if (!empty($startDate) && !empty($endDate)) {
            $conditions['user_condition'] = " AND DATE(u.created_at) BETWEEN :start_date AND :end_date";
            $conditions['partner_condition'] = " AND DATE(p.created_at) BETWEEN :start_date AND :end_date";
            $conditions['betting_condition'] = " AND DATE(pb.created_at) BETWEEN :start_date AND :end_date";
            $conditions['balance_condition'] = " AND DATE(pb.updated_at) BETWEEN :start_date AND :end_date";

            $conditions['params'][':start_date'] = $startDate;
            $conditions['params'][':end_date'] = $endDate;
        }
        // If period is provided and not default, use period-based filtering
        else if ($period && $period !== '7days') {
            $days = $this->getDaysFromPeriod($period);
            $conditions['user_condition'] = " AND DATE(u.created_at) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
            $conditions['partner_condition'] = " AND DATE(p.created_at) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
            $conditions['betting_condition'] = " AND DATE(pb.created_at) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
            $conditions['balance_condition'] = " AND DATE(pb.updated_at) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
        }
        // For default case (7days or no period), don't add date filtering to avoid parameter issues
        // This allows getting all-time stats when no specific filtering is requested

        return $conditions;
    }

    /**
     * Helper method to get date condition based on period
     * @param string $period
     * @return string
     */
    private function getDateCondition($period)
    {
        $days = $this->getDaysFromPeriod($period);
        return " AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
    }

    /**
     * Helper method to get number of days from period string
     * @param string $period
     * @return int
     */
    private function getDaysFromPeriod($period)
    {
        switch ($period) {
            case '1day':
                return 1;
            case '7days':
                return 7;
            case '30days':
                return 30;
            case '90days':
                return 90;
            default:
                return 7;
        }
    }

}
