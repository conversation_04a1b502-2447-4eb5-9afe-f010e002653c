<?php

/**
 * Services are globally registered in this file
 *
 * @var \Phalcon\Config $config
 */
use Phalcon\Di\FactoryDefault;
use Phalcon\Mvc\View;
use Phalcon\Mvc\Url as UrlResolver;
use Phalcon\Mvc\View\Engine\Volt as VoltEngine;
use Phalcon\Mvc\Model\Metadata\Memory as MetaDataAdapter;
use Phalcon\Session\Adapter\Files as SessionAdapter;
use Phalcon\Flash\Direct as Flash;
use PhpAmqpLib\Connection\AMQPConnection;

/**
 * The FactoryDefault Dependency Injector automatically register the right services providing a full stack framework
 */
$di = new FactoryDefault();

/**
 * The URL component is used to generate all kind of urls in the application
 */
$di->setShared('url', function () use ($config) {
    $url = new UrlResolver();
    $url->setBaseUri($config->application->baseUri);

    return $url;
});

$di->setShared('dbBonus', function () use ($config) {
    $dbConfig = $config->dbBonus->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbProfile', function () use ($config) {
    $dbConfig = $config->dbProfile->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbUser', function () use ($config) {
    $dbConfig = $config->dbUser->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbSms', function () use ($config) {
    $dbConfig = $config->dbSms->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbTrxn', function () use ($config) {
    $dbConfig = $config->dbTrxn->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbTrxnRead', function () use ($config) {
    $dbConfig = $config->dbTrxnRead->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbSports', function () use ($config) {
    $dbConfig = $config->dbSports->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbSportsRead', function () use ($config) {
    $dbConfig = $config->dbSportsRead->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbBets', function () use ($config) {
    $dbConfig = $config->dbBets->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbBets', function () use ($config) {
    $dbConfig = $config->dbBets->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

$di->setShared('dbBetsRead', function () use ($config) {
    $dbConfig = $config->dbBetsRead->toArray();
    $adapter = $dbConfig['adapter'];
    unset($dbConfig['adapter']);

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $adapter;

    return new $class($dbConfig);
});

/**
 *  queue configuration 
 */
$di->setShared('queue', function () use ($config) {
    $mQueueConfig = $config->mQueue->toArray();
    return $mQueueConfig;
});

/**
 * Redis
 */
$di->setShared('redis', function () use ($config) {
    $redis = new \Redis();
    $redis->pconnect($config->redis->redisServer, $config->redis->redisPortNo);
    $redis->auth($config->redis->redisAuth);
    $redis->setOption(Redis::OPT_SCAN, Redis::SCAN_RETRY);

    return $redis;
});

/**
 *  log configuration file 
 */
$di->setShared('logPath', function () use ($config) {
    $logPathConfig = $config->logPath->toArray();
    return $logPathConfig;
});

/**
 *  Mail configuration 
 */
$di->setShared('mail', function () use ($config) {
    $ussdSettingsConfig = $config->MailSettings->toArray();
    return $ussdSettingsConfig;
});
/**
 *  Settings configuration 
 */
$di->setShared('settings', function () use ($config) {
    $ussdSettingsConfig = $config->settings->toArray();
    return $ussdSettingsConfig;
});

