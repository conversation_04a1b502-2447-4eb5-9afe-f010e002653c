<?php

use Phalcon\Http\Response;
use Phalcon\Mvc\Micro;
use Phalcon\Mvc\Micro\MiddlewareInterface;

/**
 * NotFoundMiddleware
 */
class NotFoundMiddleware implements MiddlewareInterface {

    /**
     * @param Event $event
     * @param Micro $application
     *
     * @returns bool
     */
    public function before(Micro $application) {

        $res = new \stdClass();
        $res->code = "Error";
        $res->statusDescription = "Application link not found";
        $res->data = ['code' => 404, 'message' => 'The route you are looking for is NOT found.'];

        $application->response->setHeader("Content-Type", "application/json");
        $application->response->setHeader("Access-Control-Allow-Origin", "*");
        $application->response->setStatusCode(404, "NOT FOUND");
        $application->response->setContent(json_encode($res));
        $application->response->send();

        return false;
    }

    /**
     * @param Micro $application
     *
     * @returns bool
     */
    public function call(Micro $application) {
        return true;
    }

}
