worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

worker_rlimit_nofile 4096;
events {
    use epoll;
    worker_connections 4096;
    multi_accept on;
    accept_mutex off;
}

http {

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    ##request tracing using custom format
    log_format custom '$remote_addr - $remote_user [$time_local] '
                      '"$request" | status="$status" | "$http_referer"'
                      ' rt="$request_time" | uct="$upstream_connect_time" | uht="$upstream_header_time" | "tat="$upstream_response_time" ';

    ##this uses the our custom log format
    access_log /var/log/nginx/custom.log custom;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    types_hash_max_size 2048;
    keepalive_timeout 65;
    send_timeout 10;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;


    client_body_buffer_size 32k;
    client_body_in_single_buffer on;
    client_body_timeout 180s;
    client_header_timeout 180s;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_buffers 16 8k;
    gzip_comp_level 3;
    gzip_min_length 1024;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/json;

    #Open files cache
    #open_file_cache max=1024 inactive=20s;
    #open_file_cache_valid 120s;
    #open_file_cache_min_uses 2;
    #open_file_cache_errors off;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    server_names_hash_bucket_size 128;

    # Configure microcache (fastcgi)
    fastcgi_cache_path /tmp/nginx_cache levels=1:2 keys_zone=ZONE_1:100m inactive=60m;
    fastcgi_cache_key "$scheme$request_method$host$request_uri";
    add_header X-Cache $upstream_cache_status;
    add_header Web 'API-1';

    server_tokens off;

    server {
            #server_name litestaging.playbetman.com;
            set  $root_path '/var/www/apps/api';
            root $root_path;
            index index.php index.html;

            charset utf-8;

            # Load configuration files for the default server block.
            include /etc/nginx/default.d/*.conf;

            add_header X-XSS-Protection "1; mode=block";
            add_header X-Content-Type-Options "nosniff";

            # Cache by defaul
            set $no_cache 0;

            # Check for cache bypass
            if ($request_method = POST) {
                        set $no_cache 1;
            }

            # Check for cache bypass
            if ($arg_skipcache = 1) {
                set $no_cache 1;
            }

            location / {
                # Matches URLS `$_GET['_url']`
                try_files $uri $uri/ /index.php?_url=$uri&$args;
            }

            #try_files $uri $uri/ @rewrite;
            location @rewrite {
                rewrite ^/(.*)$ /index.php?_url=/$1;
            }

            location ~ \.php$ {
                fastcgi_pass  127.0.0.1:9000;
                fastcgi_index /index.php;

                include fastcgi_params;
                include /etc/nginx/fastcgi_params;
                fastcgi_split_path_info       ^(.+\.php)(/.+)$;
                fastcgi_param PATH_INFO       $fastcgi_path_info;
                fastcgi_param PATH_TRANSLATED $document_root$fastcgi_path_info;
                fastcgi_param SCRIPT_FILENAME $request_filename;
                fastcgi_connect_timeout 60;
                proxy_read_timeout 60s;
                fastcgi_send_timeout 180;
                fastcgi_read_timeout 180;
                fastcgi_buffer_size 128k;
                fastcgi_buffers 256 16k;
                fastcgi_busy_buffers_size 256k;
                fastcgi_temp_file_write_size 256k;
                fastcgi_intercept_errors on;
                fastcgi_max_temp_file_size 0;

                # Enable cache
                #fastcgi_cache ZONE_1;
                #fastcgi_cache_valid 200 6s;
                #fastcgi_cache_bypass $no_cache;
                #fastcgi_no_cache $no_cache;

                # max request size
                client_max_body_size 20m;
            }

            location ~* \.(js|css|png|jpg|jpeg|gif|ico|mp3|js|ico|html|xml|txt|svg)$ {
                add_header Cache-Control public;
                add_header Pragma public;
                add_header Vary Accept-Encoding;
                expires    max;
                log_not_found off;
                access_log    off;
            }

            location ~* ^/(css|img|js|flv|swf|download)/(.+)$ {
                root $root_path;
            }

            location ~* \.(eot|ttf|woff|woff2)$ {
                add_header Access-Control-Allow-Origin *;
            }

            #deny access to .git
            location ~ (?:^|/)\. {
                deny all;
            }

            location ~ /\.hta {
                deny all;
            }

            location ~ /\.(?!well-known).* {
                deny all;
            }

            location /robots.txt {
                return 200 "User-agent: *\nallow: /\n";
            }

    }
}