# Use the official PHP 7.4 FPM image as the base
FROM php:7.4-fpm

# -------------------------------------------------------------------
# 1. Install OS packages & build dependencies in one RUN.
#    - We also install Memcached & Redis from PECL in the same step.
#    - We remove 'git' if it’s only needed for building, not runtime.
# -------------------------------------------------------------------
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    libzip-dev \
    zip \
    nginx \
    libmemcached-dev \
    curl \
    && docker-php-ext-install zip pdo_mysql bcmath \
    && pecl install memcached psr redis \
    && docker-php-ext-enable memcached psr redis \
    # Clone & build Phalcon from source
    && git clone --depth=1 -b 4.1.x https://github.com/phalcon/cphalcon.git /tmp/cphalcon \
    && cd /tmp/cphalcon/build \
    && ./install \
    && docker-php-ext-enable phalcon \
    # Remove cphalcon and 'git' once we’re done
    && rm -rf /tmp/cphalcon \
    && apt-get remove -y git \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

# -------------------------------------------------------------------
# 2. Install Composer
# -------------------------------------------------------------------
RUN curl -sS https://getcomposer.org/installer | php -- \
    --install-dir=/usr/local/bin --filename=composer

# -------------------------------------------------------------------
# 3. Create & set up directories
# -------------------------------------------------------------------
RUN mkdir -p /var/www/apps/api \
    && chmod -R 755 /var/www/apps/api \
    && mkdir -p /var/www/logs/mossbets \
    && chown -Rf www-data:www-data /var/www/logs/mossbets \
    && chmod -R 777 /var/www/logs/mossbets
    
RUN mkdir -p /var/www/utilities/sms/processing && \
    chown -Rf www-data:www-data /var/www/utilities/sms/processing && \
    chmod -R 777 /var/www/utilities/sms/processing

WORKDIR /var/www/apps/api

# -------------------------------------------------------------------
# 4. Create an nginx system user & copy nginx config
# -------------------------------------------------------------------
RUN adduser --system --no-create-home --disabled-login nginx
COPY nginx/nginx.conf /etc/nginx/nginx.conf
RUN nginx -t -c /etc/nginx/nginx.conf

# -------------------------------------------------------------------
# 5. Copy your application code
# -------------------------------------------------------------------
COPY . .

# -------------------------------------------------------------------
# 6. Install Composer dependencies
# -------------------------------------------------------------------
ENV COMPOSER_ALLOW_SUPERUSER=1
RUN composer config --no-plugins allow-plugins.kylekatarnls/update-helper true \
    && composer install --no-dev --optimize-autoloader

# -------------------------------------------------------------------
# 7. Tweak PHP-FPM settings in one RUN to reduce layers
# -------------------------------------------------------------------
RUN sed -i '/^pm.max_children =/s/=.*/= 50/' /usr/local/etc/php-fpm.d/www.conf \
    && sed -i '/^pm =/s/=.*/= dynamic/' /usr/local/etc/php-fpm.d/www.conf \
    && sed -i '/^pm.start_servers =/s/=.*/= 5/' /usr/local/etc/php-fpm.d/www.conf \
    && sed -i '/^pm.min_spare_servers =/s/=.*/= 5/' /usr/local/etc/php-fpm.d/www.conf \
    && sed -i '/^pm.max_spare_servers =/s/=.*/= 35/' /usr/local/etc/php-fpm.d/www.conf \
    && sed -i '/^pm.process_idle_timeout =/s/=.*/= 2s/' /usr/local/etc/php-fpm.d/www.conf \
    && sed -i '/^pm.max_requests =/s/=.*/= 500/' /usr/local/etc/php-fpm.d/www.conf

# -------------------------------------------------------------------
# 8. Expose & start Nginx + PHP-FPM
# -------------------------------------------------------------------
EXPOSE 80
CMD service nginx start && php-fpm