<?php

declare(strict_types=1);

use Phalcon\Loader,
    Phalcon\Cli\Console,
    Phalcon\Cli\Dispatcher,
    Phalcon\Di\FactoryDefault,
    Phalcon\Cli\Dispatcher\Exception,
    Phalcon\Db\Adapter\Pdo\Mysql as PdoMysql,
    Phalcon\DI\FactoryDefault\CLI as CliDI;

define('S_HOST', gethostname());

$phalcon = 4;
$env = 'PROD';
$errors = 0;

$env = 'DEV';
$phalcon = 4;
if (in_array(S_HOST, ['ke-pr-web-2'])) {
    $env = 'PROD';
}
define('APP_PATH', realpath(''));
define('PHALCON_VERSION', $phalcon);
define('ENVIRONMENT', $env);

ini_set('display_errors', "$errors");
ini_set('display_startup_errors', "$errors");
ini_set("date.timezone", "Africa/Nairobi");
error_reporting(E_ALL);

try {
    /**
     * Read auto-loader
     */
    include APP_PATH . "/vendor/autoload.php";

    /**
     * Read the configuration
     */
    $config = include APP_PATH . "/app/config/config.php";

    /**
     * Create a loader
     */
    $loader = new Loader();
    $loader->registerDirs(
            [
                "app/tasks/",
                "app/controllers/",
                "app/utils/"
            ]
    )->register();

    /**
     * Read services
     */
    include APP_PATH . "/app/config/services.php";


    /**
     * Create a CLI console
     */
    $console = new Console();

    /**
     * Set the DI container
     */
    $console->setDI(new CliDI());

    /**
     * Process the command line arguments
     */
    $arguments = [];
    foreach ($argv as $k => $arg) {
        if ($k === 1) {
            ///    $arguments['task'] = $arg;
        } elseif ($k === 2) {
            $arguments['action'] = $arg;
        } elseif ($k >= 3) {
            $arguments['params'][] = $arg;
        }
    }

    /**
     * Handle the incoming arguments
     */
    $console->handle($arguments);
} catch (Exception $e) {
    echo date('Y-m-d H:i:s')
    . ' - [EMERGENCY] - ' . __LINE__
    . ' | Exception Trace::' . $e->getTraceAsString()
    . ' | Exception Message::' . $e->getMessage();
    exit(1);
}
